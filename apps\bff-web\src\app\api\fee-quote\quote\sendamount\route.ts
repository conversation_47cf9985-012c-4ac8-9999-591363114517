import { NextResponse } from 'next/server';
import { FeeQuoteClient } from '../../../../../clients/fee-quote.client';

const feeQuoteClient = new FeeQuoteClient();

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const quote = await feeQuoteClient.getQuote(body);
    return NextResponse.json(quote);
  } catch (error: any) {
    console.error('Error getting fee quote:', error);
    return NextResponse.json(
      error.error || { error: { category: 'API_ERROR', code: '500', message: 'Failed to get fee quote' } },
      { status: error.status || 500 }
    );
  }
} 