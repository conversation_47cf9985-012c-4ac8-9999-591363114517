import React from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';
import type { Agent } from '@agentworks/utils';

const StyledView = styled(View);
const StyledText = styled(Text);

interface AgentCardProps {
  agent: Agent;
}

const AgentCard: React.FC<AgentCardProps> = ({ agent }) => {
  return (
    <StyledView className="bg-white rounded-lg shadow-md p-4 mb-4">
      <StyledText className="text-xl font-bold mb-2">{agent.agentName}</StyledText>
      <StyledView className="space-y-2">
        <StyledText className="text-gray-600">Address: {agent.agentAddress}</StyledText>
        <StyledText className="text-gray-600">City: {agent.agentCity}</StyledText>
        <StyledText className="text-gray-600">State: {agent.agentState}</StyledText>
        <StyledText className="text-gray-600">Country: {agent.agentCountry}</StyledText>
        <StyledText className="text-gray-600">Status: {agent.agentStatus}</StyledText>
      </StyledView>
    </StyledView>
  );
};

export default AgentCard; 