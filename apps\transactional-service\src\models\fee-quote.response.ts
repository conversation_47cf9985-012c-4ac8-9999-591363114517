interface TransactionAmount {
  amount: {
    value: number;
    currencyCode: string;
  };
  fees?: {
    value: number;
    currencyCode: string;
  };
  taxes?: {
    value: number;
    currencyCode: string;
  };
  total: {
    value: number;
    currencyCode: string;
  };
}

interface ReceiveAmount extends TransactionAmount {
  fxRate: number;
  fxRateEstimated: boolean;
}

interface Transaction {
  transactionId: string;
  serviceOptionCode: string;
  serviceOptionName: string;
  serviceOptionRoutingCode?: string;
  estimatedDelivery: string;
  sendAmount: TransactionAmount;
  receiveAmount: ReceiveAmount;
}

export interface FeeQuoteResponse {
  transactions: Transaction[];
} 