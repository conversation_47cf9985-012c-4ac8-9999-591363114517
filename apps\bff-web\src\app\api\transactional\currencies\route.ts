import { NextResponse } from 'next/server';
import { config } from '../../../../config';

export async function GET() {
  try {
    const response = await fetch(`${config.services.transactional.url}/api/transactional/currencies`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error fetching currencies:', error);
    return NextResponse.json(
      { error: [{ category: 'API_ERROR', code: '500', message: 'Failed to fetch currencies' }] },
      { status: 500 }
    );
  }
} 