export interface SearchParams {
  userLanguage?: string;
  rewardsNumber?: string;
  identificationType?: string;
  identificationNumber?: string;
  mobilePhone?: string;
  maxProfilesToReturn?: number;
  pageNumber?: number;
  perPage?: number;
}

export interface Name {
  firstName: string;
  middleName?: string;
  lastName: string;
  secondLastName?: string;
}

export interface Address {
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  countrySubdivisionCode: string;
  countryCode: string;
  postalCode: string;
}

export interface MobilePhone {
  number: string;
  countryDialCode: string;
}

export interface Profile {
  name: Name;
  address: Address;
  mobilePhone: MobilePhone;
  rewardsNumber?: string;
  dateofBirth?: string;
  profileId: string;
}

export interface PaginationLink {
  pageNumber: number;
  href: string;
}

export interface PaginationMetadata {
  totalItems: number;
  currentPage: number;
  perPage: number;
  totalPages: number;
  links?: PaginationLink[];
}

export interface SearchResponse {
  profiles: Profile[];
  paginationMetadata: PaginationMetadata;
}