'use client';

export * from './utils/ClientOnly';

// Re-export React Native components wrapped with client-side only rendering
import { View as RNView, Text as RNText } from 'react-native';
import { ClientOnly } from './utils/ClientOnly';

export const View = (props: React.ComponentProps<typeof RNView>) => (
  <ClientOnly>
    <RNView {...props} />
  </ClientOnly>
);

export const Text = (props: React.ComponentProps<typeof RNText>) => (
  <ClientOnly>
    <RNText {...props} />
  </ClientOnly>
);

export { default as Button } from './components/Button';
export { default as UserCard } from './components/UserCard';
export { default as AgentCard } from './components/AgentCard';
export { default as DeviceCard } from './components/DeviceCard';
export { default as ProfileCard } from './components/ProfileCard'; 