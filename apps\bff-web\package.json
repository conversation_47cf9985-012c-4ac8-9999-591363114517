{"name": "@agentworks/bff-web", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env PORT=3002 ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "node dist/index.js", "clean": "rm -rf dist"}, "dependencies": {"@agentworks/transactional-service": "workspace:*", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/helmet": "^4.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "cross-env": "^7.0.3", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0"}}