import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { Button } from './Button';

const meta: Meta<typeof Button> = {
  title: 'Components/Button',
  component: Button,
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: 'select',
      options: ['primary', 'secondary', 'outline']
    },
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg']
    },
    label: {
      control: 'text'
    }
  }
};

export default meta;
type Story = StoryObj<typeof Button>;

export const Primary: Story = {
  args: {
    variant: 'primary',
    size: 'md',
    label: 'Primary Button'
  }
};

export const Secondary: Story = {
  args: {
    variant: 'secondary',
    size: 'md',
    label: 'Secondary Button'
  }
};

export const Outline: Story = {
  args: {
    variant: 'outline',
    size: 'md',
    label: 'Outline Button'
  }
};

export const Small: Story = {
  args: {
    variant: 'primary',
    size: 'sm',
    label: 'Small Button'
  }
};

export const Large: Story = {
  args: {
    variant: 'primary',
    size: 'lg',
    label: 'Large Button'
  }
}; 