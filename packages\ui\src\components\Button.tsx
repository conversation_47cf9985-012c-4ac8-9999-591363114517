import React from 'react';
import { Pressable, Text, StyleSheet, type PressableProps, ViewStyle } from 'react-native';

export interface ButtonProps extends PressableProps {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  label: string;
  style?: ViewStyle;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  label,
  style,
  disabled,
  ...props
}) => {
  return (
    <Pressable
      style={[
        styles.button,
        disabled ? styles.buttonDisabled : styles.buttonEnabled,
        style as ViewStyle
      ]}
      disabled={disabled}
      {...props}
    >
      <Text
        style={[
          styles.text,
          disabled ? styles.textDisabled : styles.textEnabled
        ]}
      >
        {label}
      </Text>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: 25,
    paddingVertical: 12,
    paddingHorizontal: 16,
    minWidth: 200,
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonEnabled: {
    backgroundColor: '#008542', // bright green for enabled state
  },
  buttonDisabled: {
    backgroundColor: '#E8E8E8', // light gray for disabled state
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
  },
  textEnabled: {
    color: '#FFFFFF', // white text for enabled state
  },
  textDisabled: {
    color: '#9E9E9E', // medium gray text for disabled state
  },
});

export default Button; 