import axios from 'axios';
import { User, Agent, Devi<PERSON>, Profile } from '@agentworks/utils';
import logger from '../config/logger';

const TRANSACTIONAL_SERVICE_URL = process.env.TRANSACTIONAL_SERVICE_URL || 'http://localhost:3000';

const client = axios.create({
  baseURL: TRANSACTIONAL_SERVICE_URL,
  timeout: 5000,
});

interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

interface GetUsersParams {
  page?: number;
  limit?: number;
  search?: string;
}

export const transactionalServiceClient = {
  // User endpoints
  async getUsers(params: GetUsersParams): Promise<PaginatedResponse<User>> {
    logger.info('Fetching users with params:', params);
    const response = await client.get('/api/users', { params });
    return response.data;
  },

  async getUser(userId: string): Promise<User> {
    logger.info(`Fetching user with ID: ${userId}`);
    const response = await client.get(`/api/users/${userId}`);
    return response.data;
  },

  async createUser(userData: User): Promise<User> {
    logger.info('Creating new user');
    const response = await client.post('/api/users', userData);
    return response.data;
  },

  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    logger.info(`Updating user with ID: ${userId}`);
    const response = await client.put(`/api/users/${userId}`, userData);
    return response.data;
  },

  // Agent endpoints
  async getAgent(agentId: string): Promise<Agent> {
    logger.info(`Fetching agent with ID: ${agentId}`);
    const response = await client.get(`/api/agents/${agentId}`);
    return response.data;
  },

  async createAgent(agentData: Agent): Promise<Agent> {
    logger.info('Creating new agent');
    const response = await client.post('/api/agents', agentData);
    return response.data;
  },

  async updateAgent(agentId: string, agentData: Partial<Agent>): Promise<Agent> {
    logger.info(`Updating agent with ID: ${agentId}`);
    const response = await client.put(`/api/agents/${agentId}`, agentData);
    return response.data;
  },

  // Device endpoints
  async getDevice(deviceId: string): Promise<Device> {
    logger.info(`Fetching device with ID: ${deviceId}`);
    const response = await client.get(`/api/devices/${deviceId}`);
    return response.data;
  },

  async createDevice(deviceData: Device): Promise<Device> {
    logger.info('Creating new device');
    const response = await client.post('/api/devices', deviceData);
    return response.data;
  },

  async updateDevice(deviceId: string, deviceData: Partial<Device>): Promise<Device> {
    logger.info(`Updating device with ID: ${deviceId}`);
    const response = await client.put(`/api/devices/${deviceId}`, deviceData);
    return response.data;
  },

  // Profile endpoints
  async getProfile(profileId: string): Promise<Profile> {
    logger.info(`Fetching profile with ID: ${profileId}`);
    const response = await client.get(`/api/profiles/${profileId}`);
    return response.data;
  },

  async createProfile(profileData: Profile): Promise<Profile> {
    logger.info('Creating new profile');
    const response = await client.post('/api/profiles', profileData);
    return response.data;
  },

  async updateProfile(profileId: string, profileData: Partial<Profile>): Promise<Profile> {
    logger.info(`Updating profile with ID: ${profileId}`);
    const response = await client.put(`/api/profiles/${profileId}`, profileData);
    return response.data;
  }
}; 