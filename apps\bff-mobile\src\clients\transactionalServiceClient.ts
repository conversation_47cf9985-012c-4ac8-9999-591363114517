import axios from 'axios';
import type { User, Agent, Device, Profile } from '@agentworks/utils';

const client = axios.create({
  baseURL: process.env.TRANSACTIONAL_SERVICE_URL || 'http://localhost:3000',
  timeout: 5000,
});

export const transactionalServiceClient = {
  // User endpoints
  async getUser(userId: string): Promise<User> {
    const { data } = await client.get(`/api/users/${userId}`);
    return data;
  },

  async createUser(userData: User): Promise<User> {
    const { data } = await client.post('/api/users', userData);
    return data;
  },

  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    const { data } = await client.put(`/api/users/${userId}`, userData);
    return data;
  },

  // Agent endpoints
  async getAgent(agentId: string): Promise<Agent> {
    const { data } = await client.get(`/api/agents/${agentId}`);
    return data;
  },

  async createAgent(agentData: Agent): Promise<Agent> {
    const { data } = await client.post('/api/agents', agentData);
    return data;
  },

  async updateAgent(agentId: string, agentData: Partial<Agent>): Promise<Agent> {
    const { data } = await client.put(`/api/agents/${agentId}`, agentData);
    return data;
  },

  // Device endpoints
  async getDevice(deviceId: string): Promise<Device> {
    const { data } = await client.get(`/api/devices/${deviceId}`);
    return data;
  },

  async createDevice(deviceData: Device): Promise<Device> {
    const { data } = await client.post('/api/devices', deviceData);
    return data;
  },

  async updateDevice(deviceId: string, deviceData: Partial<Device>): Promise<Device> {
    const { data } = await client.put(`/api/devices/${deviceId}`, deviceData);
    return data;
  },

  // Profile endpoints
  async getProfile(profileId: string): Promise<Profile> {
    const { data } = await client.get(`/api/profiles/${profileId}`);
    return data;
  },

  async createProfile(profileData: Profile): Promise<Profile> {
    const { data } = await client.post('/api/profiles', profileData);
    return data;
  },

  async updateProfile(profileId: string, profileData: Partial<Profile>): Promise<Profile> {
    const { data } = await client.put(`/api/profiles/${profileId}`, profileData);
    return data;
  }
}; 