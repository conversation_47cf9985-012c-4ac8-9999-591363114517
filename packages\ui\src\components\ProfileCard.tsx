import React from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';
import type { Profile } from '@agentworks/utils';

const StyledView = styled(View);
const StyledText = styled(Text);

interface ProfileCardProps {
  profile: Profile;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ profile }) => {
  return (
    <StyledView className="bg-white rounded-lg shadow-md p-4 mb-4">
      <StyledText className="text-xl font-bold mb-2">Profile {profile.profileNo}</StyledText>
      <StyledView className="space-y-2">
        <StyledText className="text-gray-600">ID: {profile.profileId}</StyledText>
        <StyledText className="text-gray-600">Type: {profile.profileType}</StyledText>
        <StyledText className="text-gray-600">Status: {profile.profileStatus}</StyledText>
      </StyledView>
    </StyledView>
  );
};

export default ProfileCard; 