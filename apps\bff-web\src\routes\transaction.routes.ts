import { Router, Request, Response } from 'express';
import { TransactionalClient } from '../clients/transactional.client';
import type { QuoteRequest } from '@agentworks/transactional-service/src/models/quote.request';

const router = Router();
const transactionalClient = new TransactionalClient();

// Get quote for a transaction
router.post('/quote', async (req: Request<any, any, QuoteRequest>, res: Response) => {
  try {
    const quoteRequest = req.body;
    const quote = await transactionalClient.getQuote(quoteRequest);
    res.json(quote);
  } catch (error: any) {
    res.status(error.status || 500).json(error);
  }
});

// Update transaction details
router.put('/:transactionId', async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const updateData = req.body;
    const updatedTransaction = await transactionalClient.updateTransaction(transactionId, updateData);
    res.json(updatedTransaction);
  } catch (error: any) {
    res.status(error.status || 500).json(error);
  }
});

// Commit transaction
router.put('/:transactionId/commit', async (req: Request, res: Response) => {
  try {
    const { transactionId } = req.params;
    const committedTransaction = await transactionalClient.commitTransaction(transactionId);
    res.json(committedTransaction);
  } catch (error: any) {
    res.status(error.status || 500).json(error);
  }
});

export default router; 