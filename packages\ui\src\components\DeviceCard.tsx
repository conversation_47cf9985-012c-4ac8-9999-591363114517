import React from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';
import type { Device } from '@agentworks/utils';

const StyledView = styled(View);
const StyledText = styled(Text);

interface DeviceCardProps {
  device?: Device | null;
}

const DeviceCard: React.FC<DeviceCardProps> = ({ device }) => {
  if (!device) {
    return (
      <StyledView className="bg-white rounded-lg shadow-md p-4 mb-4">
        <StyledText className="text-xl font-bold mb-2">No Device</StyledText>
        <StyledView className="space-y-2">
          <StyledText className="text-gray-600">No device information available</StyledText>
        </StyledView>
      </StyledView>
    );
  }

  return (
    <StyledView className="bg-white rounded-lg shadow-md p-4 mb-4">
      <StyledText className="text-xl font-bold mb-2">{device.name || device.deviceName || 'Unnamed Device'}</StyledText>
      <StyledView className="space-y-2">
        <StyledText className="text-gray-600">ID: {device.id || device.deviceId || 'N/A'}</StyledText>
        <StyledText className="text-gray-600">Type: {device.type || device.deviceType || 'N/A'}</StyledText>
        <StyledText className="text-gray-600">Status: {device.status || device.deviceStatus || 'N/A'}</StyledText>
        <StyledText className="text-gray-600">Security Level: {device.deviceSecurityLevel || 'N/A'}</StyledText>
      </StyledView>
    </StyledView>
  );
};

export default DeviceCard; 