import React from 'react';
import { View, ScrollView, Text } from 'react-native';
import { useUser } from '@agentworks/hooks';
import { Button } from '@agentworks/ui';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';

type RootStackParamList = {
  Home: undefined;
  Profile: { userId: string };
  Agent: { agentId: string };
  Device: { deviceId: string };
};

type Props = NativeStackScreenProps<RootStackParamList, 'Home'>;

export const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const userId = 'current-user-id'; // TODO: Get from auth context
  const { data: userData, isLoading } = useUser(userId);

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text>Loading...</Text>
      </View>
    );
  }

  return (
    <ScrollView className="flex-1 bg-gray-100">
      <View className="p-4">
        <Button
          label="View Profile"
          onPress={() => navigation.navigate('Profile', { userId })}
          variant="primary"
          className="mb-4"
        />
        
        <Button
          label="View Agent"
          onPress={() => navigation.navigate('Agent', { agentId: userId })}
          variant="secondary"
          className="mb-4"
        />
        
        <Button
          label="View Devices"
          onPress={() => navigation.navigate('Device', { deviceId: userId })}
          variant="outline"
        />
      </View>
    </ScrollView>
  );
};