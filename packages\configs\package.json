{"name": "@agentworks/configs", "version": "0.0.1", "private": true, "files": ["eslint-config-custom", "tsconfig.base.json", "tsconfig.next.json", "tailwind.config.js"], "dependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-config-next": "^14.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.0.0", "eslint-plugin-react-hooks": "^4.0.0"}}