{"version": "0.2.0", "configurations": [{"type": "node", "request": "launch", "name": "Debug Transactional Service", "skipFiles": ["<node_internals>/**"], "program": "${workspaceFolder}/apps/transactional-service/src/index.ts", "outFiles": ["${workspaceFolder}/apps/transactional-service/dist/**/*.js"], "sourceMaps": true, "resolveSourceMapLocations": ["${workspaceFolder}/**", "!**/node_modules/**"], "preLaunchTask": "tsc: build - apps/transactional-service/tsconfig.json"}]}