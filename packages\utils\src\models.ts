import { z } from 'zod';

export const UserSchema = z.object({
  userId: z.string(),
  firstName: z.string(),
  lastName: z.string(),
  emailId: z.string().email(),
  agentId: z.string(),
  mainOfficeId: z.string(),
  country: z.string(),
  preferredLanguage: z.string()
});

export const AgentSchema = z.object({
  agentId: z.string(),
  agentName: z.string(),
  agentAddress: z.string(),
  agentCity: z.string(),
  agentState: z.string(),
  agentCountry: z.string(),
  agentStatus: z.enum(['active', 'inactive'])
});

export const DeviceSchema = z.object({
  deviceId: z.string(),
  deviceName: z.string(),
  deviceType: z.string(),
  deviceStatus: z.enum(['active', 'inactive']),
  deviceSecurityLevel: z.enum(['high', 'medium', 'low'])
});

export const ProfileSchema = z.object({
  profileId: z.string(),
  profileNo: z.string(),
  profileType: z.string(),
  profileStatus: z.enum(['active', 'inactive'])
});

export type User = z.infer<typeof UserSchema>;
export type Agent = z.infer<typeof AgentSchema>;
export type Device = z.infer<typeof DeviceSchema>;
export type Profile = z.infer<typeof ProfileSchema>; 