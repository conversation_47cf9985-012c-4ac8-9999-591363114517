import { z } from 'zod';

export const UserSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  userId: z.string(),
  emailId: z.string().email(),
  agentId: z.string(),
  mainOfficeId: z.string(),
  hqAccountNo: z.string(),
  country: z.string(),
  preferredLanguage: z.string()
});

export const AgentSchema = z.object({
  agentName: z.string(),
  agentAddress: z.string(),
  agentCity: z.string(),
  agentState: z.string(),
  agentCountry: z.string(),
  agentStatus: z.string()
});

export const DeviceSchema = z.object({
  deviceName: z.string(),
  deviceId: z.string(),
  deviceStatus: z.string(),
  deviceSecurityLevel: z.string(),
  deviceType: z.string()
});

export const ProfileSchema = z.object({
  profileId: z.string(),
  profileNo: z.string(),
  profileStatus: z.string(),
  profileType: z.string()
});

export type User = z.infer<typeof UserSchema>;
export type Agent = z.infer<typeof AgentSchema>;
export type Device = z.infer<typeof DeviceSchema>;
export type Profile = z.infer<typeof ProfileSchema>; 