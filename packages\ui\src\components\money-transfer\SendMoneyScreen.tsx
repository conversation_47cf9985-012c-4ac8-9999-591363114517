import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Card } from '../common/Card';
import { Section } from '../common/Section';
import { FeeLookup } from './FeeLookup';
import { CustomerSearch } from './CustomerSearch';
import type { Profile } from './CustomerSearch';

export interface SendMoneyScreenProps {
  // Props will be added as we develop features
}

export const SendMoneyScreen: React.FC<SendMoneyScreenProps> = () => {
  const [selectedCustomer, setSelectedCustomer] = useState<Profile | null>(null);

  const handleGetQuote = () => {
    // TODO: Implement quote fetching logic
    console.log('Getting transaction quote...');
  };

  const handleCustomerSelect = (customer: Profile) => {
    setSelectedCustomer(customer);
    // TODO: Update receiver information based on selected customer
  };

  return (
    <View style={styles.container}>
      {/* Customer Search Section */}
      <Section title="Customer Search">
        <Card style={styles.customerSearchSection}>
          <View style={styles.sectionContent}>
            <CustomerSearch onCustomerSelect={handleCustomerSelect} />
          </View>
        </Card>
      </Section>

      {/* Two Column Layout for Receiver and Fee Lookup */}
      <View style={styles.twoColumnLayout}>
        {/* Receiver Information Section */}
        <Section title="Receiver Information" style={styles.receiverSection}>
          <Card>
            <View style={styles.sectionContent}>
              {/* Receiver information fields will be added here */}
            </View>
          </Card>
        </Section>

        {/* Fee Lookup Section */}
        <Section title="" style={styles.feeLookupSection}>
          <Card>
            <FeeLookup onGetQuote={handleGetQuote} />
          </Card>
        </Section>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#f5f5f5',
  },
  customerSearchSection: {
    marginBottom: 16,
  },
  twoColumnLayout: {
    flexDirection: 'row',
    gap: 16,
  },
  receiverSection: {
    flex: 1,
  },
  feeLookupSection: {
    flex: 1,
  },
  sectionContent: {
    padding: 16,
  },
}); 