import React from 'react';
import { View, StyleSheet } from 'react-native';

interface Currency {
  code: string;
  name: string;
}

const CURRENCIES: Currency[] = [
  { code: 'USD', name: 'US Dollar' },
  { code: 'EUR', name: 'Euro' },
  { code: 'MXN', name: 'Mexican Peso' },
  { code: 'CAD', name: 'Canadian Dollar' },
];

interface CurrencyDropdownProps {
  value: Currency;
  onChange: (currency: Currency) => void;
}

export const CurrencyDropdown: React.FC<CurrencyDropdownProps> = ({ value, onChange }) => {
  const selectStyle = {
    width: '100%',
    height: 36,
    border: '1px solid #ccc',
    borderRadius: 4,
    backgroundColor: '#fff',
    padding: '0 8px',
    fontSize: 14,
    color: '#333',
    appearance: 'none' as const,
    cursor: 'pointer',
  };

  return (
    <View style={styles.container}>
      <select
        value={value.code}
        onChange={(e) => {
          const selected = CURRENCIES.find(c => c.code === e.target.value);
          if (selected) {
            onChange(selected);
          }
        }}
        style={selectStyle}
        aria-label="Select currency"
        title="Select currency"
      >
        {CURRENCIES.map((currency) => (
          <option key={currency.code} value={currency.code}>
            {`${currency.code} - ${currency.name}`}
          </option>
        ))}
      </select>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
}); 