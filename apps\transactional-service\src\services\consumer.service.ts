import { SearchParams, SearchResponse } from '../models/consumer.model';
import { MoneyGramConsumerService } from './moneygram-consumer.service';
import logger from '../config/logger';
import { v4 as uuidv4 } from 'uuid';

export class ConsumerService {
  private moneyGramService: MoneyGramConsumerService;

  constructor() {
    this.moneyGramService = new MoneyGramConsumerService();
  }

  async search(params: SearchParams): Promise<SearchResponse> {
    const requestId = uuidv4();

    logger.info('=== Consumer Search Request Started ===', {
      requestId,
      timestamp: new Date().toISOString(),
      searchParams: {
        ...params,
        // Mask sensitive data
        identificationNumber: params.identificationNumber ? '****' : undefined,
        mobilePhone: params.mobilePhone ? '****' : undefined,
        rewardsNumber: params.rewardsNumber ? '****' : undefined
      }
    });

    try {
      // Validate search parameters
      if (!params.identificationType && !params.mobilePhone && !params.rewardsNumber) {
        throw new Error('At least one search criteria must be provided: identificationType with identificationNumber, mobilePhone, or rewardsNumber');
      }

      if (params.identificationType && !params.identificationNumber) {
        throw new Error('identificationNumber is required when identificationType is provided');
      }

      const startTime = Date.now();
      const result = await this.moneyGramService.searchProfiles(params);
      const duration = Date.now() - startTime;

      logger.info('=== Consumer Search Response Success ===', {
        requestId,
        timestamp: new Date().toISOString(),
        duration: `${duration}ms`,
        profileCount: result.profiles.length,
        totalItems: result.paginationMetadata.totalItems
      });

      return result;
    } catch (error: any) {
      logger.error('=== Consumer Search Error ===', {
        requestId,
        timestamp: new Date().toISOString(),
        errorDetails: {
          message: error.message,
          code: error.code,
          response: error.response?.data,
          stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
        }
      });

      throw error;
    }
  }
}