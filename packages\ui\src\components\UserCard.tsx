import React from 'react';
import { View, Text } from 'react-native';
import { styled } from 'nativewind';
import type { User } from '@agentworks/utils';

const StyledView = styled(View);
const StyledText = styled(Text);

interface UserCardProps {
  user: User;
}

const UserCard: React.FC<UserCardProps> = ({ user }) => {
  return (
    <StyledView className="bg-white rounded-lg shadow-md p-4 mb-4">
      <StyledText className="text-xl font-bold mb-2">
        {user.firstName} {user.lastName}
      </StyledText>
      <StyledView className="space-y-2">
        <StyledText className="text-gray-600">Email: {user.emailId}</StyledText>
        <StyledText className="text-gray-600">Country: {user.country}</StyledText>
        <StyledText className="text-gray-600">
          Preferred Language: {user.preferredLanguage}
        </StyledText>
        <StyledText className="text-gray-600">Agent ID: {user.agentId}</StyledText>
        <StyledText className="text-gray-600">
          Main Office ID: {user.mainOfficeId}
        </StyledText>
      </StyledView>
    </StyledView>
  );
};

export default UserCard; 