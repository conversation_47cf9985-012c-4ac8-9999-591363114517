import { Router } from 'express';
import asyncHandler from 'express-async-handler';
import { userService } from '../services/userService';

const router = Router();

router.get('/:userId', asyncHandler(async (req, res) => {
  const user = await userService.getUser(req.params.userId);
  res.json(user);
}));

router.post('/', asyncHandler(async (req, res) => {
  const user = await userService.createUser(req.body);
  res.status(201).json(user);
}));

router.put('/:userId', asyncHandler(async (req, res) => {
  const user = await userService.updateUser(req.params.userId, req.body);
  res.json(user);
}));

export default router; 