import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet } from 'react-native';
import Button from '../Button';
import { config } from '../../config';

interface TransactionQuoteProps {
  onGetQuote?: () => void;
}

export const TransactionQuote: React.FC<TransactionQuoteProps> = ({ onGetQuote }) => {
  const [amount, setAmount] = useState('');
  const [promoCode, setPromoCode] = useState('');
  const [selectedServiceDetails, setSelectedServiceDetails] = useState({
    serviceType: 'Cash Pickup - Anywhere',
    exchangeRate: '1 MXN = 0.078272 CAD',
    transferFee: '150 MXN',
    totalToPay: '384 MXN',
    recipientGets: '18.32 CAD',
    estimatedDelivery: 'A Few Minutes'
  });

  return (
    <View style={styles.container}>
      <View style={styles.formSection}>
        <View style={styles.row}>
          <View style={styles.field}>
            <Text style={styles.label}>Amount</Text>
            <TextInput
              style={styles.input}
              value={amount}
              onChangeText={setAmount}
              placeholder="Enter amount"
              keyboardType="numeric"
            />
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Fee Type</Text>
            <TextInput
              style={styles.input}
              value="Send Amount (excluding fee)"
              editable={false}
            />
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Send Currency</Text>
            <TextInput
              style={styles.input}
              value="MXN - Mexican Peso"
              editable={false}
            />
          </View>
        </View>

        <View style={styles.row}>
          <View style={styles.field}>
            <Text style={styles.label}>Destination Country</Text>
            <TextInput
              style={styles.input}
              value="CAN - Canada"
              editable={false}
            />
          </View>

          <View style={styles.field}>
            <Text style={styles.label}>Destination Province</Text>
            <TextInput
              style={styles.input}
              placeholder="Select Province"
            />
          </View>
        </View>

        <View style={styles.promoSection}>
          <Text style={styles.label}>Promo Code</Text>
          <TextInput
            style={styles.input}
            value={promoCode}
            onChangeText={setPromoCode}
            placeholder="Enter promo code"
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button
            label="Get Transaction Quote"
            onPress={onGetQuote}
            style={styles.button}
          />
        </View>

        {/* Selected Service Details Section */}
        <View style={styles.serviceDetailsSection}>
          <Text style={styles.sectionTitle}>Selected Service Details</Text>
          <View style={styles.serviceDetailsGrid}>
            {/* First Row */}
            <View style={styles.detailsRow}>
              <View style={styles.field}>
                <Text style={styles.label}>Service Type:</Text>
                <TextInput
                  style={styles.input}
                  value={selectedServiceDetails.serviceType}
                  editable={false}
                />
              </View>

              <View style={styles.field}>
                <Text style={styles.label}>Exchange Rate:</Text>
                <TextInput
                  style={styles.input}
                  value={selectedServiceDetails.exchangeRate}
                  editable={false}
                />
              </View>

              <View style={styles.field}>
                <Text style={styles.label}>Transfer Fee:</Text>
                <TextInput
                  style={styles.input}
                  value={selectedServiceDetails.transferFee}
                  editable={false}
                />
              </View>
            </View>

            {/* Second Row */}
            <View style={styles.detailsRow}>
              <View style={styles.field}>
                <Text style={styles.label}>Total to Pay:</Text>
                <TextInput
                  style={styles.input}
                  value={selectedServiceDetails.totalToPay}
                  editable={false}
                />
              </View>

              <View style={styles.field}>
                <Text style={styles.label}>Recipient Gets:</Text>
                <TextInput
                  style={styles.input}
                  value={selectedServiceDetails.recipientGets}
                  editable={false}
                />
              </View>

              <View style={styles.field}>
                <Text style={styles.label}>Estimated Delivery:</Text>
                <TextInput
                  style={styles.input}
                  value={selectedServiceDetails.estimatedDelivery}
                  editable={false}
                />
              </View>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  formSection: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    gap: 16,
  },
  row: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  field: {
    flex: 1,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
    backgroundColor: '#fff',
  },
  serviceDetailsSection: {
    marginTop: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#008000',
    marginBottom: 16,
  },
  serviceDetailsGrid: {
    gap: 16,
  },
  detailsRow: {
    flexDirection: 'row',
    gap: 16,
  },
  promoSection: {
    marginBottom: 24,
  },
  buttonContainer: {
    alignItems: 'flex-end',
  },
  button: {
    minWidth: 200,
  },
}); 