import React from 'react';
import { View } from 'react-native';
import ReactDOM from 'react-dom';

interface PortalProps {
  children: React.ReactNode;
}

export const Portal: React.FC<PortalProps> = ({ children }) => {
  const [portalContainer] = React.useState(() => {
    const div = document.createElement('div');
    div.style.position = 'absolute';
    div.style.top = '0';
    div.style.left = '0';
    div.style.right = '0';
    div.style.zIndex = '9999';
    div.style.pointerEvents = 'none';
    return div;
  });

  React.useEffect(() => {
    document.body.appendChild(portalContainer);
    return () => {
      document.body.removeChild(portalContainer);
    };
  }, [portalContainer]);

  return ReactDOM.createPortal(
    <View style={{ position: 'absolute', width: '100%', pointerEvents: 'auto' }}>
      {children}
    </View>,
    portalContainer
  );
}; 