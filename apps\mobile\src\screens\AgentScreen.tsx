import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import type { NativeStackScreenProps } from '@react-navigation/native-stack';

type RootStackParamList = {
  Home: undefined;
  Profile: { userId: string };
  Agent: { agentId: string };
  Device: { deviceId: string };
};

type Props = NativeStackScreenProps<RootStackParamList, 'Agent'>;

export const AgentScreen: React.FC<Props> = ({ route, navigation }) => {
  const { agentId } = route.params;

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Agent Details</Text>
      <Text style={styles.subtitle}>Agent ID: {agentId}</Text>
      <Text style={styles.description}>
        This is the agent details screen. Here you can view agent information, 
        performance metrics, and manage agent settings.
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  subtitle: {
    fontSize: 18,
    marginBottom: 20,
    color: '#666',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    color: '#888',
    lineHeight: 24,
  },
});
