{"name": "@agentworks/web", "version": "0.0.1", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" next dev -p 3003", "build": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" next build", "start": "cross-env NODE_OPTIONS=\"--max-old-space-size=2048\" next start -p 3003", "lint": "next lint", "clean": "if exist .turbo rmdir /s /q .turbo && if exist node_modules rmdir /s /q node_modules && if exist .next rmdir /s /q .next"}, "dependencies": {"@agentworks/hooks": "workspace:*", "@agentworks/ui": "workspace:*", "@agentworks/utils": "workspace:*", "@react-native-masked-view/masked-view": "^0.3.2", "@tanstack/react-query": "^5.0.0", "axios": "^1.6.0", "cross-env": "^7.0.3", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.50.0", "react-native": "0.72.17", "react-native-svg-web": "^1.0.9", "react-native-web": "0.19.10"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.0.0", "@babel/preset-flow": "^7.27.1", "@babel/preset-react": "^7.27.1", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-native": "0.72.8", "autoprefixer": "^10.0.0", "babel-loader": "^10.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "postcss": "^8.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}}