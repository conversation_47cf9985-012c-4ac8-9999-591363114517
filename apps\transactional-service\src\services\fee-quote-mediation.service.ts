import axios, { AxiosInstance } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { FeeQuoteRequest } from '../models/fee-quote.request';
import { FeeQuoteResponse } from '../models/fee-quote.response';
import https from 'https';

export class FeeQuoteMediationService {
  private client: AxiosInstance;

  constructor() {
    // Configure axios with HTTPS agent for development
    const httpsAgent = process.env.NODE_ENV === 'development' 
      ? new https.Agent({ rejectUnauthorized: false })
      : undefined;

    this.client = axios.create({
      baseURL: process.env.MONEYGRAM_CORE_SERVICE_URL,
      httpsAgent
    });
  }

  async getQuote(quoteRequest: FeeQuoteRequest): Promise<FeeQuoteResponse> {
    try {
      // Construct the specific payload with env values
      const payload = {
        targetAudience: "AGENT_FACING",
        agentPartnerId: process.env.MONEYGRAM_PARTNER_ID,
        posId: process.env.MONEYGRAM_POS_ID,
        operatorId: process.env.MONEYGRAM_OPERATOR_ID,
        userLanguage: process.env.MONEYGRAM_USER_LANGUAGE,
        ...quoteRequest
      };

      const response = await this.client.post<FeeQuoteResponse>(
        '/ipfeequotemediationservice/v1/transfers/quote/sendamount',
        payload,
        {
          headers: {
            'accept': 'application/json',
            'X-MG-ClientRequestId': uuidv4(),
            'X-MG-RequestId': uuidv4(),
            'Content-Type': 'application/json'
          }
        }
      );

      console.log('MoneyGram Fee Quote API Response:', JSON.stringify(response.data, null, 2));

      return response.data;
    } catch (error: any) {
      console.log('MoneyGram Fee Quote API Error Response:', JSON.stringify(error.response?.data, null, 2));
      throw this.handleError(error);
    }
  }

  private handleError(error: any): any {
    // Log the error for debugging
    console.log('Fee Quote Service Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      stack: error.stack
    });

    // If the error is already in the correct format, return it
    if (error.response?.data?.error && 
        typeof error.response.data.error === 'object' &&
        !Array.isArray(error.response.data.error)) {
      return error.response.data;
    }

    // Handle network errors or cases where response is undefined
    if (!error.response) {
      return {
        error: {
          category: 'NETWORK_ERROR',
          code: 'CONNECTION_ERROR',
          message: error.message || 'Network error occurred',
          offendingFields: []
        }
      };
    }

    // Transform to the desired format
    return {
      error: {
        category: error.response.status ? 'API_ERROR' : 'SYSTEM_ERROR',
        code: String(error.response.status || 'UNKNOWN'),
        message: error.response.data?.message || error.message || 'Unknown error',
        offendingFields: error.response.data?.offendingFields?.map((field: any) => ({
          field: field.field || field
        })) || []
      }
    };
  }
} 