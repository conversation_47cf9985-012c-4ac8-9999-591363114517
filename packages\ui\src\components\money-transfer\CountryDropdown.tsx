import React from 'react';
import { View, StyleSheet } from 'react-native';

interface Country {
  code: string;
  name: string;
}

const COUNTRIES: Country[] = [
  { code: 'USA', name: 'United States' },
  { code: 'CAN', name: 'Canada' },
  { code: 'MEX', name: 'Mexico' },
  { code: 'GBR', name: 'United Kingdom' },
  { code: 'FRA', name: 'France' },
  { code: 'DEU', name: 'Germany' },
  { code: 'ITA', name: 'Italy' },
  { code: 'ESP', name: 'Spain' },
  { code: 'AUS', name: 'Australia' },
  { code: 'JPN', name: 'Japan' },
  { code: 'CHN', name: 'China' },
  { code: 'IND', name: 'India' },
  { code: 'BRA', name: 'Brazil' },
  { code: 'ARG', name: 'Argentina' },
  { code: 'ZAF', name: 'South Africa' },
];

interface CountryDropdownProps {
  value: Country;
  onChange: (country: Country) => void;
}

export const CountryDropdown: React.FC<CountryDropdownProps> = ({ value, onChange }) => {
  const selectStyle = {
    width: '100%',
    height: 36,
    border: '1px solid #ccc',
    borderRadius: 4,
    backgroundColor: '#fff',
    padding: '0 8px',
    fontSize: 14,
    color: '#333',
    appearance: 'none' as const,
    cursor: 'pointer',
  };

  return (
    <View style={styles.container}>
      <select
        value={value.code}
        onChange={(e) => {
          const selected = COUNTRIES.find(c => c.code === e.target.value);
          if (selected) {
            onChange(selected);
          }
        }}
        style={selectStyle}
        aria-label="Select country"
        title="Select country"
      >
        {COUNTRIES.map((country) => (
          <option key={country.code} value={country.code}>
            {`${country.code} - ${country.name}`}
          </option>
        ))}
      </select>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
}); 