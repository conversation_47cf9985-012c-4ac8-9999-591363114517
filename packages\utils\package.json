{"name": "@agentworks/utils", "version": "0.0.1", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --watch --dts", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@agentworks/configs": "workspace:*", "eslint": "^8.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}}