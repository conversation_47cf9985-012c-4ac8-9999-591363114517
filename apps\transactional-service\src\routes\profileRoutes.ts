import { Router } from 'express';
import asyncHandler from 'express-async-handler';
import { profileService } from '../services/profileService';

const router = Router();

router.get('/:profileId', asyncHandler(async (req, res) => {
  const profile = await profileService.getProfile(req.params.profileId);
  res.json(profile);
}));

router.post('/', asyncHandler(async (req, res) => {
  const profile = await profileService.createProfile(req.body);
  res.status(201).json(profile);
}));

router.put('/:profileId', asyncHandler(async (req, res) => {
  const profile = await profileService.updateProfile(req.params.profileId, req.body);
  res.json(profile);
}));

export default router; 