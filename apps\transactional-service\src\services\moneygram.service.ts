import axios, { AxiosInstance,  AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { config } from '../config';
import { AuthService } from './auth.service';
import { QuoteRequest } from '../models/quote.request';
import { QuoteResponse } from '../models/quote.response';
import logger from '../config/logger';

export interface Amount {
  currencyCode: string;
  value: number;
}

export interface AdditionalCharge {
  typeCode: string;
  label: string;
  value: string;
  currencyCode: string;
}

export interface DiscountDetails {
  totalDiscount: string;
  promotionDetails?: string[];
}

export interface TransactionAmount {
  amount: Amount;
  fees?: Amount;
  taxes?: Amount;
  additionalCharges?: AdditionalCharge[];
  discountsApplied?: DiscountDetails;
  total: Amount;
}

export interface TransactionUpdate {
  sender: {
    name: {
      firstName: string;
      lastName: string;
    };
    address: {
      line1: string;
      city: string;
      countryCode: string;
      postalCode: string;
    };
    identification: {
      type: string;
      number: string;
    };
  };
  receiver: {
    name: {
      firstName: string;
      lastName: string;
    };
  };
  targetAccount?: {
    accountNumber: string;
    bankCode?: string;
  };
}

interface MoneyGramErrorResponse {
  error: {
    category: string;
    code: string;
    message: string;
    offendingFields?: Array<{
      field: string;
    }>;
  };
}

interface CurrencyResponse {
  currencies: Array<{
    code: string;
    name: string;
    isActive: boolean;
    decimalPlaces: number;
  }>;
}

export class MoneyGramService {
  private client: AxiosInstance;
  private authService: AuthService;

  constructor() {
    this.client = axios.create({
      baseURL: config.moneyGram.apiBaseUrl
    });
    this.authService = AuthService.getInstance();

    // Add request interceptor only for URL handling
    this.client.interceptors.request.use(async (axiosConfig: InternalAxiosRequestConfig) => {
      // Add environment to URL if not already present
      const url = axiosConfig.url || '';
      const environment = config.moneyGram.environment;
      if (!url.startsWith(`/${environment}`)) {
        axiosConfig.url = `/${environment}${url}`;
      }
      return axiosConfig;
    });
  }

  async getQuote(quoteRequest: QuoteRequest): Promise<QuoteResponse> {
    try {
      const token = await this.authService.getAccessToken();
      
      // Construct the specific payload
      const payload = {
        targetAudience: "AGENT_FACING",
        agentPartnerId: config.moneyGram.partnerId,
        destinationCountryCode: quoteRequest.destinationCountryCode,
        serviceOptionCode: "WILL_CALL",
        sendAmount: {
          value: quoteRequest.sendAmount,
          currencyCode: "USD"
        }
      };

      const response = await this.client.post<QuoteResponse>(
        '/transfer/v1/transactions/quote',
        payload,
        {
          headers: {
            'X-MG-ClientRequestId': uuidv4(),
            'accept': 'application/json',
            'authorization': `Bearer ${token}`,
            'content-type': 'application/json'
          }
        }
      );

      console.log('MoneyGram API Response:', JSON.stringify(response.data, null, 2));

      return response.data;
    } catch (error: any) {
      console.log('MoneyGram API Error Response:', JSON.stringify(error.response?.data, null, 2));
      throw this.handleError(error);
    }
  }

  async updateTransaction(transactionId: string, updateData: any) {
    try {
      const token = await this.authService.getAccessToken();
      
      const headers = {
        'X-MG-ClientRequestId': uuidv4(),
        'accept': 'application/json',
        'authorization': `Bearer ${token}`,
        'content-type': 'application/json'
      };

      const response = await this.client.put(
        `/transfer/v1/transactions/${transactionId}`,
        updateData,
        { headers }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async commitTransaction(transactionId: string) {
    try {
      const token = await this.authService.getAccessToken();
      
      const headers = {
        'X-MG-ClientRequestId': uuidv4(),
        'accept': 'application/json',
        'authorization': `Bearer ${token}`,
        'content-type': 'application/json'
      };

      const response = await this.client.put(
        `/transfer/v1/transactions/${transactionId}/commit`,
        {},
        { headers }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async getCurrencies(): Promise<CurrencyResponse> {
    try {
      const token = await this.authService.getAccessToken();
      
      const headers = {
        'X-MG-ClientRequestId': uuidv4(),
        'accept': 'application/json',
        'authorization': `Bearer ${token}`,
        'content-type': 'application/json'
      };

      const response = await this.client.get<CurrencyResponse>(
        '/reference-data/v1/currencies?userLanguage=en-US',
        { headers }
      );

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private handleError(error: any): any {
    // Log the error for debugging
    console.log('MoneyGram Service Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message,
      stack: error.stack
    });

    // If the error is already in the correct format, return it
    if (error.response?.data?.error && 
        typeof error.response.data.error === 'object' &&
        !Array.isArray(error.response.data.error)) {
      return error.response.data;
    }

    // Handle network errors or cases where response is undefined
    if (!error.response) {
      return {
        error: {
          category: 'NETWORK_ERROR',
          code: 'CONNECTION_ERROR',
          message: error.message || 'Network error occurred',
          offendingFields: []
        }
      };
    }

    // Transform to the desired format
    return {
      error: {
        category: error.response.status ? 'API_ERROR' : 'SYSTEM_ERROR',
        code: String(error.response.status || 'UNKNOWN'),
        message: error.response.data?.message || error.message || 'Unknown error',
        offendingFields: error.response.data?.offendingFields?.map((field: any) => ({
          field: field.field || field
        })) || []
      }
    };
  }
} 