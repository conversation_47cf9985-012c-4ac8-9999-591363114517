import { settings } from './settings';

// Load and validate environment variables
const validateEnvVariables = () => {
  const requiredVars = [
    'MONEYGRAM_API_URL',
    'MONEYGRAM_CLIENT_ID',
    'MONEYGRAM_CLIENT_SECRET',
    '<PERSON><PERSON><PERSON><PERSON>GRAM_PARTNER_ID',
    'MON<PERSON>YGRAM_ENV',
    'MONEYGRAM_CORE_SERVICE_URL',
    'MONEYGRAM_POS_ID',
    'MONEYGRAM_OPERATOR_ID',
    'MONEYGRAM_USER_LANGUAGE'
  ];

  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
  }
};

// Run validation
validateEnvVariables();

const baseUrl = process.env.MONEYGRAM_API_URL || 'https://qaapi.qa.moneygram.com';
const env = process.env.MONEYGRAM_ENV || 'q5';

interface MoneyGramConfig {
  baseUrl: string;
  apiBaseUrl: string;
  apiKey: string;
  partnerId: string;
  clientId: string;
  clientSecret: string;
  environment: string;
  posId: string;
  operatorId: string;
  userLanguage: string;
  coreServiceUrl: string;
}

export const config = {
  moneyGram: {
    baseUrl: settings.moneyGram.apiUrl,
    apiBaseUrl: settings.moneyGram.apiUrl,
    apiKey: '',
    partnerId: settings.moneyGram.partnerId,
    clientId: settings.moneyGram.clientId,
    clientSecret: settings.moneyGram.clientSecret,
    environment: settings.moneyGram.env,
    posId: settings.moneyGram.posId,
    operatorId: settings.moneyGram.operatorId,
    userLanguage: settings.moneyGram.userLanguage,
    coreServiceUrl: settings.moneyGram.coreServiceUrl
  }
}; 