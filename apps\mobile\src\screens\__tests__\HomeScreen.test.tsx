import React from 'react';
import { render, fireEvent, waitFor } from '@testing-library/react-native';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { HomeScreen } from '../HomeScreen';

const mockNavigation = {
  navigate: jest.fn()
};

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false
    }
  }
});

describe('HomeScreen', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    const { getByText } = render(
      <QueryClientProvider client={queryClient}>
        <HomeScreen navigation={mockNavigation as any} />
      </QueryClientProvider>
    );

    expect(getByText('Loading...')).toBeTruthy();
  });

  it('navigates to Profile screen when profile button is pressed', async () => {
    const { getByText } = render(
      <QueryClientProvider client={queryClient}>
        <HomeScreen navigation={mockNavigation as any} />
      </QueryClientProvider>
    );

    await waitFor(() => {
      const profileButton = getByText('View Profile');
      fireEvent.press(profileButton);
    });

    expect(mockNavigation.navigate).toHaveBeenCalledWith('Profile', {
      userId: 'current-user-id'
    });
  });

  it('navigates to Agent screen when agent button is pressed', async () => {
    const { getByText } = render(
      <QueryClientProvider client={queryClient}>
        <HomeScreen navigation={mockNavigation as any} />
      </QueryClientProvider>
    );

    await waitFor(() => {
      const agentButton = getByText('View Agent');
      fireEvent.press(agentButton);
    });

    expect(mockNavigation.navigate).toHaveBeenCalledWith('Agent', {
      agentId: 'current-user-id'
    });
  });
}); 