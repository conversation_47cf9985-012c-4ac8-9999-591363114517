import { ConsumerClient, ConsumerSearchParams, SearchResponse } from '../clients/consumer.client';

export class ConsumerService {
  private consumerClient: ConsumerClient;

  constructor() {
    this.consumerClient = new ConsumerClient();
  }

  async searchConsumers(params: ConsumerSearchParams): Promise<SearchResponse> {
    try {
      return await this.consumerClient.search(params);
    } catch (error) {
      // Log error or handle specific error cases
      throw error;
    }
  }
} 