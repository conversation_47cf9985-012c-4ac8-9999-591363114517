{"extends": "@agentworks/configs/tsconfig.base.json", "compilerOptions": {"target": "ES2019", "lib": ["DOM", "DOM.Iterable", "ESNext"], "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "declaration": true, "declarationMap": true, "sourceMap": true, "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}