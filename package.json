{"name": "agentworks", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "prettier": "^3.0.0", "turbo": "^1.12.0", "typescript": "^5.0.0"}, "packageManager": "pnpm@10.8.1", "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@types/express": "^5.0.2", "@types/helmet": "^4.0.0", "@types/uuid": "^9.0.7", "express": "^5.1.0", "helmet": "^7.0.0", "posthog-js": "^1.242.2", "posthog-react-native": "^3.15.3", "react-native-device-info": "^14.0.4", "uuid": "^9.0.0"}}