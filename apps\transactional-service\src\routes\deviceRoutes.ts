import { Router } from 'express';
import asyncHandler from 'express-async-handler';
import { deviceService } from '../services/deviceService';

const router = Router();

router.get('/:deviceId', asyncHandler(async (req, res) => {
  const device = await deviceService.getDevice(req.params.deviceId);
  res.json(device);
}));

router.post('/', asyncHandler(async (req, res) => {
  const device = await deviceService.createDevice(req.body);
  res.status(201).json(device);
}));

router.put('/:deviceId', asyncHandler(async (req, res) => {
  const device = await deviceService.updateDevice(req.params.deviceId, req.body);
  res.json(device);
}));

export default router; 