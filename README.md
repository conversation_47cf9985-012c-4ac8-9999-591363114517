# AgentWorks Monorepo

A TypeScript-based monorepo project using Turborepo and pnpm workspaces. AgentWorks is a money transfer application that integrates with MoneyGram APIs to provide consumer search, fee quotes, and transaction processing capabilities. The project supports cross-platform development using React Native with Expo and EAS, React Native for Web, and Node.js backend services.

## 📱 Applications

- `mobile`: React Native (Expo + EAS) mobile app for iOS and Android
- `web`: React Native for Web app focused on money transfer operations
- `bff-mobile`: Node.js (TypeScript) Backend-for-Frontend (BFF) for mobile
- `bff-web`: Node.js (TypeScript) BFF for web
- `transactional-service`: Core Node.js (TypeScript) backend service with MoneyGram API integration

## 🧱 Project Structure

```bash
/apps
  /mobile              # React Native mobile app
  /web                 # React Native Web app (Send Money focused)
  /bff-mobile          # Mobile Backend-for-Frontend
  /bff-web             # Web Backend-for-Frontend
  /transactional-service # Core backend with MoneyGram integration
/packages
  /ui                  # Shared UI components (Money Transfer, Customer Search)
  /hooks               # Shared React hooks
  /utils               # Shared utilities and models
  /configs             # Shared configurations (TypeScript, ESLint, Tailwind)
  /analytics           # Analytics service with PostHog integration
```

## ⚙️ Tech Stack

- **Frontend**
  - React Native (mobile)
  - React Native Web
  - Tailwind CSS / NativeWind
  - Zustand (state management)
  - React Query (server state)
  - React Hook Form + Zod

- **Backend**
  - Node.js
  - TypeScript
  - Express
  - Axios (HTTP client)
  - MoneyGram API Integration

- **Analytics**
  - PostHog (event tracking)

- **Testing**
  - Jest
  - React Testing Library
  - Playwright (Web E2E)
  - Maestro (Mobile E2E)
  - MSW (API mocking)

- **Build & Tools**
  - Turborepo
  - pnpm
  - TypeScript
  - ESLint
  - Prettier

## 🏦 MoneyGram Integration

AgentWorks integrates with MoneyGram APIs to provide comprehensive money transfer services:

### Features
- **Consumer Search**: Search customers by Driver License, Mobile Phone, or Rewards Number
- **Fee Quote Calculator**: Get real-time fee quotes for money transfers
- **Transaction Processing**: Process money transfer transactions
- **Multi-currency Support**: Support for various currencies and countries

### API Endpoints
- Consumer Search: `https://extintsvcs.aws.moneygram.com/searchconsumerprofileservice/profiles/search`
- Fee Quote: `https://extintsvcs.aws.moneygram.com/ipfeequotemediationservice/v1/transfers/quote/sendamount`

### Supported Search Methods
1. **Driver License Search**: `identificationType=Drivers License&identificationNumber=**********`
2. **Mobile Phone Search**: `mobilePhone=**********`
3. **Rewards Number Search**: `rewardsNumber=251110955431`

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- pnpm 8+

### Installation

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Build all packages:
   ```bash
   pnpm build
   ```

3. Start development:
   ```bash
   pnpm dev
   ```

### Environment Configuration

Create `.env` files in the respective services with MoneyGram API credentials:

```bash
# apps/transactional-service/.env
MONEYGRAM_CORE_SERVICE_URL=https://extintsvcs.aws.moneygram.com
MONEYGRAM_PARTNER_ID=30164505
MONEYGRAM_POS_ID=4
MONEYGRAM_OPERATOR_ID=sasi
MONEYGRAM_USER_LANGUAGE=en-US
```

## 📦 Available Scripts

- `pnpm build`: Build all packages and applications
- `pnpm dev`: Start development servers
- `pnpm lint`: Run ESLint
- `pnpm test`: Run tests
- `pnpm clean`: Clean all build artifacts

## 📱 Mobile Development

1. Install Expo CLI:
   ```bash
   pnpm add -g expo-cli
   ```

2. Start the mobile app:
   ```bash
   cd apps/mobile
   pnpm start
   ```

## 🌐 Web Development

The web application is focused on money transfer operations with a single route: `/money-transfer/send-money`

### Development Servers

1. **Start all services** (recommended):
   ```bash
   pnpm dev
   ```

2. **Or start individually**:
   ```bash
   # Transactional Service (Port 3000)
   cd apps/transactional-service && pnpm dev

   # BFF-Web (Port 3002)
   cd apps/bff-web && pnpm dev

   # Web App (Port 3003)
   cd apps/web && pnpm dev
   ```

### Application URLs
- **Web App**: http://localhost:3003 (redirects to /money-transfer/send-money)
- **BFF-Web**: http://localhost:3002
- **Transactional Service**: http://localhost:3000

### API Testing
- **Consumer Search**: `GET http://localhost:3000/api/consumer/search?identificationType=Drivers%20License&identificationNumber=**********`
- **Fee Quote**: `POST http://localhost:3000/api/fee-quote/quote/sendamount`
- **Debug URL**: `GET http://localhost:3000/api/consumer/debug-url`

## 🔧 Configuration

- TypeScript configurations are in `packages/configs/tsconfig.base.json`
- ESLint configurations are in `packages/configs/eslint-config-custom`
- Tailwind configurations are in `packages/configs/tailwind.config.js`

## 📝 License

MIT

## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/topics/git/add_files/#add-files-to-a-git-repository) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin https://gitlab.com/moneygram1/workloads_grp/*********************/agentworks.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](https://gitlab.com/moneygram1/workloads_grp/*********************/agentworks/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/user/project/merge_requests/auto_merge/)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.
