import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, Modal, Pressable } from 'react-native';
import Button from '../Button';
import { CurrencyDropdown } from './CurrencyDropdown';
import { CountryDropdown } from './CountryDropdown';
import { StateDropdown } from './StateDropdown';

interface FeeQuotePayload {
  destinationCountryCode: string;
  receiverSameAsSender: boolean;
  sendAmountIncludingFee: boolean;
  sendAmount: {
    value: string;
    currencyCode: string;
  };
  receiveCurrencyCode?: string;
}

interface MoneyAmount {
  value: number;
  currencyCode: string;
}

interface TransactionAmount {
  amount: MoneyAmount;
  fees: MoneyAmount;
  taxes: MoneyAmount;
  total: MoneyAmount;
}

interface ReceiveAmount extends TransactionAmount {
  fxRate: number;
  fxRateEstimated: boolean;
}

interface Transaction {
  transactionId: string;
  serviceOptionCode: string;
  serviceOptionName: string;
  serviceOptionRoutingCode?: string;
  estimatedDelivery: string;
  sendAmount: TransactionAmount;
  receiveAmount: ReceiveAmount;
}

interface FeeQuoteResponse {
  transactions: Transaction[];
}

interface ApiError {
  message: string;
  code?: string;
}

interface FeeLookupProps {
  onGetQuote?: () => void;
}

const FEE_TYPES = [
  { value: 'excluding', label: 'Send Amount (excluding fee)' },
  { value: 'including', label: 'Send Amount (including fee)' },
  { value: 'receive', label: 'Receive Amount' },
];

export const FeeLookup: React.FC<FeeLookupProps> = ({ onGetQuote }) => {
  const [amount, setAmount] = useState('');
  const [promoCode, setPromoCode] = useState('');
  const [selectedFeeType, setSelectedFeeType] = useState<'excluding' | 'including' | 'receive'>('excluding');
  const [selectedCurrency, setSelectedCurrency] = useState({ code: 'USD', name: 'US Dollar' });
  const [selectedCountry, setSelectedCountry] = useState({ code: 'CAN', name: 'Canada' });
  const [selectedState, setSelectedState] = useState<{ code: string; name: string } | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [feeQuoteResults, setFeeQuoteResults] = useState<FeeQuoteResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedTransactionId, setSelectedTransactionId] = useState<string | null>(null);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);

  const handleFeeTypeSelect = (type: 'excluding' | 'including' | 'receive') => {
    setSelectedFeeType(type);
  };

  const handleCountryChange = (country: typeof selectedCountry) => {
    setSelectedCountry(country);
    setSelectedState(null); // Reset state when country changes
  };

  const handleGetQuote = async () => {
    try {
      setError(null);

      const payload: FeeQuotePayload = {
        destinationCountryCode: selectedCountry.code,
        receiverSameAsSender: true,
        sendAmountIncludingFee: selectedFeeType === 'including',
        sendAmount: {
          value: amount,
          currencyCode: selectedCurrency.code
        }
        // receiveCurrencyCode is optional, so we're not including it for now
      };

      const response = await fetch('/api/fee-quote/quote/sendamount', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const data = await response.json();

      if (!response.ok) {
        const errorData = data as ApiError;
        throw new Error(errorData.message || 'Failed to fetch fee quote');
      }

      setFeeQuoteResults(data);
      setIsModalVisible(true);
    } catch (err) {
      console.error('Fee quote error:', err);
      setError(err instanceof Error ? err.message : 'Failed to get fee quote. Please try again.');
      setIsModalVisible(true); // Show modal with error
    }
  };

  const handleTransactionSelect = (transaction: Transaction) => {
    setSelectedTransaction(transaction);
    setIsModalVisible(false);
  };

  const feeTypeSelectStyle = {
    width: '100%',
    height: 36,
    border: '1px solid #ccc',
    borderRadius: 4,
    backgroundColor: '#fff',
    padding: '0 8px',
    fontSize: 14,
    color: '#333',
    appearance: 'none' as const,
    cursor: 'pointer',
  };

  const showStateDropdown = selectedCountry.code === 'USA' || selectedCountry.code === 'CAN';

  const isFormValid = () => {
    return (
      amount.trim() !== '' &&
      selectedCurrency.code !== '' &&
      selectedCountry.code !== ''
    );
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Transaction Quote</Text>

      {/* First row: Amount, Fee Type, and Send Currency */}
      <View style={styles.row}>
        <View style={[styles.formGroup, styles.flex1]}>
          <Text style={styles.label}>Amount:</Text>
          <TextInput
            style={styles.input}
            value={amount}
            onChangeText={setAmount}
            inputMode="decimal"
            placeholder="Enter amount"
          />
        </View>

        <View style={[styles.formGroup, styles.flex1]}>
          <Text style={styles.label}>Fee Type</Text>
          <select
            value={selectedFeeType}
            onChange={(e) => handleFeeTypeSelect(e.target.value as typeof selectedFeeType)}
            style={feeTypeSelectStyle}
            aria-label="Select fee type"
          >
            {FEE_TYPES.map((type) => (
              <option key={type.value} value={type.value}>
                {type.label}
              </option>
            ))}
          </select>
        </View>

        <View style={[styles.formGroup, styles.flex1]}>
          <Text style={styles.label}>Send Currency</Text>
          <CurrencyDropdown
            value={selectedCurrency}
            onChange={setSelectedCurrency}
          />
        </View>
      </View>

      {/* Second row: Destination Country and State/Province */}
      <View style={styles.row}>
        <View style={[styles.formGroup, styles.flex1]}>
          <Text style={styles.label}>Destination Country</Text>
          <CountryDropdown
            value={selectedCountry}
            onChange={handleCountryChange}
          />
        </View>

        <View style={[styles.formGroup, styles.flex1]}>
          <Text style={styles.label}>
            Destination {selectedCountry.code === 'USA' ? 'State' : 'Province'}
          </Text>
          {showStateDropdown ? (
            <StateDropdown
              countryCode={selectedCountry.code}
              value={selectedState}
              onChange={setSelectedState}
            />
          ) : (
            <View style={styles.disabledInput}>
              <Text style={styles.disabledText}>Not applicable</Text>
            </View>
          )}
        </View>
      </View>

      {/* Selected Transaction Details */}
      {selectedTransaction && (
        <View style={styles.selectedTransactionDetails}>
          <Text style={styles.detailsTitle}>Selected Service Details</Text>

          <View style={styles.detailsGrid}>
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Service Type:</Text>
              <Text style={styles.detailValue}>{selectedTransaction.serviceOptionName}</Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Exchange Rate:</Text>
              <Text style={styles.detailValue}>
                1 {selectedTransaction.sendAmount.amount.currencyCode} = {' '}
                {selectedTransaction.receiveAmount.fxRate.toFixed(6)} {' '}
                {selectedTransaction.receiveAmount.amount.currencyCode}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Transfer Fee:</Text>
              <Text style={styles.detailValue}>
                {selectedTransaction.sendAmount.fees.value} {selectedTransaction.sendAmount.fees.currencyCode}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Total to Pay:</Text>
              <Text style={styles.detailValue}>
                {selectedTransaction.sendAmount.total.value} {selectedTransaction.sendAmount.total.currencyCode}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Recipient Gets:</Text>
              <Text style={styles.detailValue}>
                {selectedTransaction.receiveAmount.total.value} {selectedTransaction.receiveAmount.total.currencyCode}
              </Text>
            </View>

            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Estimated Delivery:</Text>
              <Text style={styles.detailValue}>{selectedTransaction.estimatedDelivery}</Text>
            </View>
          </View>
        </View>
      )}

      <View style={styles.formGroup}>
        <Text style={styles.label}>Promo Code</Text>
        <TextInput
          style={styles.input}
          value={promoCode}
          onChangeText={setPromoCode}
          placeholder="Enter promo code"
          inputMode="text"
        />
      </View>

      <View style={styles.buttonContainer}>
        <Button
          label="Get Transaction Quote"
          onPress={handleGetQuote}
          style={styles.button}
          disabled={!isFormValid()}
        />
      </View>

      <Modal
        visible={isModalVisible}
        transparent={true}
        animationType="fade"
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Fee Quote Results</Text>

            {error ? (
              <Text style={styles.errorText}>{error}</Text>
            ) : feeQuoteResults?.transactions ? (
              <View style={styles.resultsGrid}>
                <View style={styles.gridHeader}>
                  <Text style={[styles.headerCell, { flex: 0.5 }]}>Select</Text>
                  <Text style={styles.headerCell}>Service Type</Text>
                  <Text style={styles.headerCell}>Fee</Text>
                  <Text style={styles.headerCell}>Total to Pay</Text>
                  <Text style={styles.headerCell}>Receive Amount</Text>
                  <Text style={styles.headerCell}>Exchange Rate</Text>
                  <Text style={styles.headerCell}>Delivery Time</Text>
                </View>
                {feeQuoteResults.transactions.map((transaction) => (
                  <Pressable
                    key={transaction.transactionId}
                    style={[
                      styles.gridRow,
                      selectedTransaction?.transactionId === transaction.transactionId && styles.selectedRow
                    ]}
                    onPress={() => handleTransactionSelect(transaction)}
                  >
                    <View style={[styles.cell, { flex: 0.5 }]}>
                      <View style={styles.radioOuter}>
                        <View
                          style={[
                            styles.radioInner,
                            selectedTransaction?.transactionId === transaction.transactionId && styles.radioInnerSelected
                          ]}
                        />
                      </View>
                    </View>
                    <Text style={styles.cell}>{transaction.serviceOptionName}</Text>
                    <Text style={styles.cell}>
                      {transaction.sendAmount.fees.value} {transaction.sendAmount.fees.currencyCode}
                    </Text>
                    <Text style={styles.cell}>
                      {transaction.sendAmount.total.value} {transaction.sendAmount.total.currencyCode}
                    </Text>
                    <Text style={styles.cell}>
                      {transaction.receiveAmount.total.value} {transaction.receiveAmount.total.currencyCode}
                    </Text>
                    <Text style={styles.cell}>
                      {transaction.receiveAmount.fxRate.toFixed(6)}
                    </Text>
                    <Text style={styles.cell}>{transaction.estimatedDelivery}</Text>
                  </Pressable>
                ))}
              </View>
            ) : (
              <Text style={styles.loadingText}>Loading results...</Text>
            )}
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'left',
  },
  formGroup: {
    marginBottom: 16,
  },
  row: {
    flexDirection: 'row',
    gap: 16,
  },
  flex1: {
    flex: 1,
  },
  flex2: {
    flex: 2,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  select: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
    backgroundColor: '#f5f5f5',
  },
  disabledInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    backgroundColor: '#f5f5f5',
  },
  disabledText: {
    color: '#666',
    fontSize: 14,
  },
  buttonContainer: {
    marginTop: 16,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    minWidth: 200,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-start',
    alignItems: 'flex-end',
    paddingTop: 80,
    paddingRight: 20,
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 20,
    width: '50%',
    maxWidth: 800,
    maxHeight: '80%',
    marginTop: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 20,
    textAlign: 'center',
  },
  resultsGrid: {
    flex: 1,
    marginBottom: 20,
  },
  gridHeader: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
  headerCell: {
    flex: 1,
    fontWeight: 'bold',
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 8,
  },
  gridRow: {
    flexDirection: 'row',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  cell: {
    flex: 1,
    fontSize: 14,
    textAlign: 'center',
    paddingHorizontal: 8,
  },
  errorText: {
    color: 'red',
    textAlign: 'center',
    marginBottom: 20,
  },
  loadingText: {
    textAlign: 'center',
    marginBottom: 20,
  },
  selectedTransactionDetails: {
    marginTop: 24,
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e9ecef',
  },
  detailsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#008542',
  },
  detailsGrid: {
    gap: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  detailLabel: {
    fontSize: 14,
    color: '#495057',
    fontWeight: '500',
  },
  detailValue: {
    fontSize: 14,
    color: '#212529',
    fontWeight: '600',
  },
  selectedRow: {
    backgroundColor: '#f0f9ff',
  },
  radioOuter: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#008542',
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 'auto',
  },
  radioInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'transparent',
  },
  radioInnerSelected: {
    backgroundColor: '#008542',
  },
});