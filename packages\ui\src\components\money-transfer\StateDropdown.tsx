import React from 'react';
import { View, StyleSheet } from 'react-native';

interface State {
  code: string;
  name: string;
}

const US_STATES: State[] = [
  { code: 'AL', name: 'Alabama' },
  { code: 'AK', name: 'Alaska' },
  { code: 'AZ', name: 'Arizona' },
  { code: 'AR', name: 'Arkansas' },
  { code: 'CA', name: 'California' },
  { code: 'CO', name: 'Colorado' },
  { code: 'CT', name: 'Connecticut' },
  { code: 'DE', name: 'Delaware' },
  { code: 'FL', name: 'Florida' },
  { code: 'GA', name: 'Georgia' },
  { code: 'HI', name: 'Hawaii' },
  { code: 'ID', name: 'Idaho' },
  { code: 'IL', name: 'Illinois' },
  { code: 'IN', name: 'Indiana' },
  { code: 'IA', name: 'Iowa' },
  { code: 'KS', name: 'Kansas' },
  { code: 'KY', name: 'Kentucky' },
  { code: 'LA', name: 'Louisiana' },
  { code: 'ME', name: 'Maine' },
  { code: '<PERSON>', name: 'Maryland' },
  { code: 'MA', name: 'Massachusetts' },
  { code: 'MI', name: 'Michigan' },
  { code: 'M<PERSON>', name: 'Minnesota' },
  { code: 'MS', name: 'Mississippi' },
  { code: 'MO', name: 'Missouri' },
  { code: 'MT', name: 'Montana' },
  { code: 'NE', name: 'Nebraska' },
  { code: 'NV', name: 'Nevada' },
  { code: 'NH', name: 'New Hampshire' },
  { code: 'NJ', name: 'New Jersey' },
  { code: 'NM', name: 'New Mexico' },
  { code: 'NY', name: 'New York' },
  { code: 'NC', name: 'North Carolina' },
  { code: 'ND', name: 'North Dakota' },
  { code: 'OH', name: 'Ohio' },
  { code: 'OK', name: 'Oklahoma' },
  { code: 'OR', name: 'Oregon' },
  { code: 'PA', name: 'Pennsylvania' },
  { code: 'RI', name: 'Rhode Island' },
  { code: 'SC', name: 'South Carolina' },
  { code: 'SD', name: 'South Dakota' },
  { code: 'TN', name: 'Tennessee' },
  { code: 'TX', name: 'Texas' },
  { code: 'UT', name: 'Utah' },
  { code: 'VT', name: 'Vermont' },
  { code: 'VA', name: 'Virginia' },
  { code: 'WA', name: 'Washington' },
  { code: 'WV', name: 'West Virginia' },
  { code: 'WI', name: 'Wisconsin' },
  { code: 'WY', name: 'Wyoming' },
];

const CANADIAN_PROVINCES: State[] = [
  { code: 'AB', name: 'Alberta' },
  { code: 'BC', name: 'British Columbia' },
  { code: 'MB', name: 'Manitoba' },
  { code: 'NB', name: 'New Brunswick' },
  { code: 'NL', name: 'Newfoundland and Labrador' },
  { code: 'NS', name: 'Nova Scotia' },
  { code: 'NT', name: 'Northwest Territories' },
  { code: 'NU', name: 'Nunavut' },
  { code: 'ON', name: 'Ontario' },
  { code: 'PE', name: 'Prince Edward Island' },
  { code: 'QC', name: 'Quebec' },
  { code: 'SK', name: 'Saskatchewan' },
  { code: 'YT', name: 'Yukon' },
];

interface StateDropdownProps {
  countryCode: string;
  value: State | null;
  onChange: (state: State) => void;
}

export const StateDropdown: React.FC<StateDropdownProps> = ({ countryCode, value, onChange }) => {
  const states = countryCode === 'USA' ? US_STATES : countryCode === 'CAN' ? CANADIAN_PROVINCES : [];

  const selectStyle = {
    width: '100%',
    height: 36,
    border: '1px solid #ccc',
    borderRadius: 4,
    backgroundColor: '#fff',
    padding: '0 8px',
    fontSize: 14,
    color: '#333',
    appearance: 'none' as const,
    cursor: 'pointer',
  };

  if (!states.length) {
    return null;
  }

  return (
    <View style={styles.container}>
      <select
        value={value?.code || ''}
        onChange={(e) => {
          const selected = states.find(s => s.code === e.target.value);
          if (selected) {
            onChange(selected);
          }
        }}
        style={selectStyle}
        aria-label="Select state or province"
        title="Select state or province"
      >
        <option value="">Select {countryCode === 'USA' ? 'State' : 'Province'}</option>
        {states.map((state) => (
          <option key={state.code} value={state.code}>
            {`${state.code} - ${state.name}`}
          </option>
        ))}
      </select>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
}); 