{"name": "@agentworks/mobile", "version": "0.0.1", "private": true, "main": "expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build": "tsc", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "test": "jest", "lint": "eslint \"src/**/*.{ts,tsx}\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@agentworks/hooks": "workspace:*", "@agentworks/ui": "workspace:*", "@agentworks/utils": "workspace:*", "@react-navigation/native": "^6.0.0", "@react-navigation/native-stack": "^6.0.0", "@tanstack/react-query": "^5.0.0", "expo": "~49.0.23", "expo-status-bar": "~1.6.0", "nativewind": "^2.0.0", "react": "^18.0.0", "react-native": "0.72.6", "react-native-safe-area-context": "^4.0.0", "react-native-screens": "^3.0.0"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@babel/core": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/runtime": "^7.0.0", "@expo/cli": "^0.10.0", "@react-native/metro-config": "^0.72.0", "@types/jest": "^29.0.0", "@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "babel-jest": "^29.0.0", "babel-preset-expo": "~9.5.2", "eas-cli": "^5.9.0", "eslint": "^8.0.0", "jest": "^29.0.0", "metro-react-native-babel-preset": "^0.76.0", "react-test-renderer": "^18.0.0", "tailwindcss": "^3.3.0", "typescript": "^5.0.0"}}