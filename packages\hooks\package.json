{"name": "@agentworks/hooks", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts --external react", "dev": "tsup src/index.ts --format cjs,esm --watch --dts --external react", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@agentworks/utils": "workspace:*", "@tanstack/react-query": "^5.0.0", "axios": "^1.6.0"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@types/react": "^18.0.0", "eslint": "^8.0.0", "react": "^18.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0"}}