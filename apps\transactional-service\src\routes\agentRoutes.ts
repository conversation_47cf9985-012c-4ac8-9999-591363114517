import { Router } from 'express';
import asyncHandler from 'express-async-handler';
import { agentService } from '../services/agentService';

const router = Router();

router.get('/:agentId', asyncHandler(async (req, res) => {
  const agent = await agentService.getAgent(req.params.agentId);
  res.json(agent);
}));

router.post('/', asyncHandler(async (req, res) => {
  const agent = await agentService.createAgent(req.body);
  res.status(201).json(agent);
}));

router.put('/:agentId', asyncHandler(async (req, res) => {
  const agent = await agentService.updateAgent(req.params.agentId, req.body);
  res.json(agent);
}));

export default router; 