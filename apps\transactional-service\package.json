{"name": "@agentworks/transactional-service", "version": "1.0.0", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development PORT=3000 ts-node-dev --respawn --transpile-only src/index.ts", "dev:qa": "cross-env NODE_ENV=qa PORT=3000 ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "start": "cross-env NODE_ENV=production PORT=3000 node dist/index.js", "start:qa": "cross-env NODE_ENV=qa PORT=3000 node dist/index.js", "clean": "rm -rf dist"}, "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "uuid": "^9.0.0"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@types/axios": "^0.14.0", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/helmet": "^4.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "@types/uuid": "^9.0.7", "cross-env": "^7.0.3", "ts-node-dev": "^2.0.0", "typescript": "^5.0.0"}}