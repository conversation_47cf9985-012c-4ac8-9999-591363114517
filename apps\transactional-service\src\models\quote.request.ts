export interface MoneyAmount {
  currencyCode: string;
  value: number;
}

export interface QuoteRequest {
  // Required fields
  targetAudience: 'AGENT_FACING' | 'CONSUMER_FACING';
  agentPartnerId: string;
  userLanguage: string;
  destinationCountryCode: string;

  // Optional fields
  destinationCountrySubdivisionCode?: string;
  serviceOptionCode?: 'WILL_CALL' | 'BANK_DEPOSIT' | 'HOME_DELIVERY' | 'MOBILE_WALLET' | string;
  
  // Either sendAmount or receiveAmount must be provided
  sendAmount?: MoneyAmount;
  receiveAmount?: MoneyAmount;
  
  // Optional currency and promotion fields
  receiveCurrencyCode?: string;
  promotionCodes?: string[];
  rewardsNumber?: string;
} 