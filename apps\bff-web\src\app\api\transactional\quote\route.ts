import { NextResponse } from 'next/server';
import { config } from '../../../../config';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    
    const response = await fetch(`${config.services.transactional.url}/api/transactional/quote`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body)
    });

    if (!response.ok) {
      const errorData = await response.json();
      return NextResponse.json(errorData, { status: response.status });
    }

    const data = await response.json();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error getting quote:', error);
    return NextResponse.json(
      { error: [{ category: 'API_ERROR', code: '500', message: 'Failed to get quote' }] },
      { status: 500 }
    );
  }
} 