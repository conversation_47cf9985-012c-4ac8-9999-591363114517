import fs from 'fs';
import path from 'path';

interface MoneyGramSettings {
  apiUrl: string;
  env: string;
  clientId: string;
  clientSecret: string;
  partnerId: string;
  posId: string;
  operatorId: string;
  userLanguage: string;
  coreServiceUrl: string;
}

interface Settings {
  port: number;
  nodeEnv: string;
  logLevel: string;
  moneyGram: MoneyGramSettings;
  cors: {
    origin: string;
  };
}

function loadSettings(): Settings {
  const env = process.env.NODE_ENV || 'development';
  const settingsPath = path.join(__dirname, `settings.${env}.json`);

  try {
    if (!fs.existsSync(settingsPath)) {
      throw new Error(`Settings file not found for environment: ${env}`);
    }

    const settingsContent = fs.readFileSync(settingsPath, 'utf-8');
    const settings: Settings = JSON.parse(settingsContent);

    // Override with environment variables if they exist
    if (process.env.PORT) settings.port = parseInt(process.env.PORT, 10);
    if (process.env.LOG_LEVEL) settings.logLevel = process.env.LOG_LEVEL;
    if (process.env.MONEYGRAM_API_URL) settings.moneyGram.apiUrl = process.env.MONEYGRAM_API_URL;
    if (process.env.MONEYGRAM_ENV) settings.moneyGram.env = process.env.MONEYGRAM_ENV;
    if (process.env.MONEYGRAM_CLIENT_ID) settings.moneyGram.clientId = process.env.MONEYGRAM_CLIENT_ID;
    if (process.env.MONEYGRAM_CLIENT_SECRET) settings.moneyGram.clientSecret = process.env.MONEYGRAM_CLIENT_SECRET;
    if (process.env.MONEYGRAM_PARTNER_ID) settings.moneyGram.partnerId = process.env.MONEYGRAM_PARTNER_ID;
    if (process.env.MONEYGRAM_POS_ID) settings.moneyGram.posId = process.env.MONEYGRAM_POS_ID;
    if (process.env.MONEYGRAM_OPERATOR_ID) settings.moneyGram.operatorId = process.env.MONEYGRAM_OPERATOR_ID;
    if (process.env.MONEYGRAM_USER_LANGUAGE) settings.moneyGram.userLanguage = process.env.MONEYGRAM_USER_LANGUAGE;
    if (process.env.MONEYGRAM_CORE_SERVICE_URL) settings.moneyGram.coreServiceUrl = process.env.MONEYGRAM_CORE_SERVICE_URL;
    if (process.env.CORS_ORIGIN) settings.cors.origin = process.env.CORS_ORIGIN;

    return settings;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to load settings: ${error.message}`);
    }
    throw error;
  }
}

export const settings = loadSettings(); 