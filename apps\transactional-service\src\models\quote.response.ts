export interface AdditionalCharge {
  typeCode: string;
  label: string;
  value: string;
  currencyCode: string;
}

export interface DiscountDetails {
  totalDiscount: string;
  promotionDetails?: string[];
}

export interface TransactionAmount {
  amount: {
    currencyCode: string;
    value: string;  // Using string to handle precise decimal values
  };
  fees?: {
    currencyCode: string;
    value: string;
  };
  taxes?: {
    currencyCode: string;
    value: string;
  };
  additionalCharges?: AdditionalCharge[];
  discountsApplied?: DiscountDetails;
  total: {
    currencyCode: string;
    value: string;
  };
}

export interface TransactionQuote {
  transactionId: string;
  serviceOptionCode: string;
  serviceOptionName: string;
  serviceOptionRoutingName?: string;
  estimatedDelivery: string;
  sendAmount: TransactionAmount;
  receiveAmount: TransactionAmount & {
    fxRate: number;
    fxRateEstimated?: boolean;
  };
}

export interface QuoteResponse {
  transactions: TransactionQuote[];
}

// Error response interface
export interface QuoteErrorResponse {
  status: number;
  message: string;
  errors?: Array<{
    code: string;
    message: string;
    field?: string;
  }>;
} 