import { Router } from 'express';
import { ConsumerController } from '../controllers/consumer.controller';

const router: Router = Router();
const consumerController = new ConsumerController();

// Search endpoint
router.get('/search', (req, res) => consumerController.search(req, res));

// Debug endpoint to test URL construction
router.get('/debug-url', (req, res) => consumerController.debugUrl(req, res));

export default router;