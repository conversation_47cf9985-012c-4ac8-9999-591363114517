import { create } from 'zustand';

interface AppState {
  isDarkMode: boolean;
  setDarkMode: (isDark: boolean) => void;
  selectedLanguage: string;
  setSelectedLanguage: (lang: string) => void;
}

export const useAppStore = create<AppState>((set) => ({
  isDarkMode: false,
  setDarkMode: (isDark) => set({ isDarkMode: isDark }),
  selectedLanguage: 'en',
  setSelectedLanguage: (lang) => set({ selectedLanguage: lang }),
})); 