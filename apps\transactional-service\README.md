# Transactional Service

This service handles core business logic for the AgentWorks platform.

## Environment Variables

Create a `.env` file in the root of this service with the following variables:

```bash
PORT=3000
NODE_ENV=development
LOG_LEVEL=info
```

## API Endpoints

### Users
- `GET /api/users/:userId` - Get user by ID
- `POST /api/users` - Create a new user
- `PUT /api/users/:userId` - Update a user

### Agents
- `GET /api/agents/:agentId` - Get agent by ID
- `POST /api/agents` - Create a new agent
- `PUT /api/agents/:agentId` - Update an agent

### Devices
- `GET /api/devices/:deviceId` - Get device by ID
- `POST /api/devices` - Create a new device
- `PUT /api/devices/:deviceId` - Update a device

### Profiles
- `GET /api/profiles/:profileId` - Get profile by ID
- `POST /api/profiles` - Create a new profile
- `PUT /api/profiles/:profileId` - Update a profile

## Development

1. Install dependencies:
   ```bash
   pnpm install
   ```

2. Start development server:
   ```bash
   pnpm dev
   ```

3. Build for production:
   ```bash
   pnpm build
   ```

4. Start production server:
   ```bash
   pnpm start
   ```

## Testing

Run tests:
```bash
pnpm test
``` 