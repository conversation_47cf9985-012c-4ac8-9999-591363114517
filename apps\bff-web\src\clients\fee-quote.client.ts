import axios, { AxiosInstance } from 'axios';
import { config } from '../config';

// Import the types from transactional service
import type { FeeQuoteRequest } from '@agentworks/transactional-service/src/models/fee-quote.request';
import type { FeeQuoteResponse } from '@agentworks/transactional-service/src/models/fee-quote.response';

export class FeeQuoteClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.services.transactional.url,
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  async getQuote(quoteRequest: FeeQuoteRequest): Promise<FeeQuoteResponse> {
    try {
      const response = await this.client.post<FeeQuoteResponse>(
        '/api/fee-quote/quote/sendamount',
        quoteRequest
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        throw error.response.data;
      }
      throw {
        error: {
          category: 'API_ERROR',
          code: '500',
          message: error.message || 'Failed to get fee quote'
        }
      };
    }
  }
} 