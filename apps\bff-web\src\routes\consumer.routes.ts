import { Router } from 'express';
import { ConsumerService } from '../services/consumer.service';
import logger from '../config/logger';

const router: Router = Router();
const consumerService = new ConsumerService();

router.get('/search', async (req, res) => {
  try {
    logger.info('Consumer search request received', { query: req.query });

    const searchParams = {
      userLanguage: req.query.userLanguage as string,
      rewardsNumber: req.query.rewardsNumber as string,
      identificationType: req.query.identificationType as string,
      identificationNumber: req.query.identificationNumber as string,
      mobilePhone: req.query.mobilePhone as string,
      maxProfilesToReturn: req.query.maxProfilesToReturn ? parseInt(req.query.maxProfilesToReturn as string) : undefined,
      pageNumber: req.query.pageNumber ? parseInt(req.query.pageNumber as string) : undefined,
      perPage: req.query.perPage ? parseInt(req.query.perPage as string) : undefined
    };

    const result = await consumerService.searchConsumers(searchParams);
    logger.info('Consumer search completed successfully');
    res.json(result);
  } catch (error) {
    logger.error('Error in consumer search:', error);
    res.status(500).json({
      error: 'Internal server error',
      message: error instanceof Error ? error.message : 'Unknown error occurred',
      details: process.env.NODE_ENV === 'development' ? error : undefined
    });
  }
});

export default router; 