{"name": "@agentworks/tests", "version": "0.0.1", "private": true, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --watch --dts", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "chromatic": "chromatic"}, "dependencies": {"@agentworks/utils": "workspace:*", "@playwright/test": "^1.40.0", "@storybook/react": "^7.0.0", "@storybook/react-native": "^6.5.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/react": "^14.0.0", "@testing-library/react-native": "^12.0.0", "@testing-library/user-event": "^14.0.0", "chromatic": "^7.0.0", "jest-environment-jsdom": "^29.0.0", "msw": "^2.0.0", "playwright": "^1.40.0"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@storybook/addon-essentials": "^7.0.0", "@storybook/addon-interactions": "^7.0.0", "@storybook/addon-links": "^7.0.0", "@storybook/blocks": "^7.0.0", "@storybook/react-webpack5": "^7.0.0", "@storybook/testing-library": "^0.2.0", "@types/jest": "^29.0.0", "@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "@types/testing-library__jest-dom": "^5.14.0", "eslint": "^8.0.0", "eslint-plugin-storybook": "^0.6.0", "jest": "^29.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}}