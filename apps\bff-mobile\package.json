{"name": "@agentworks/bff-mobile", "version": "0.0.1", "private": true, "scripts": {"dev": "tsup src/index.ts --format cjs --watch --onSuccess \"node dist/index.js\"", "build": "tsup src/index.ts --format cjs", "start": "node dist/index.js", "lint": "eslint \"src/**/*.ts\"", "test": "jest", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@agentworks/utils": "workspace:*", "axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.18.0", "express-async-handler": "^1.2.0", "helmet": "^7.0.0", "http-proxy-middleware": "^2.0.0", "morgan": "^1.10.0", "winston": "^3.11.0", "zod": "^3.22.0"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@types/cors": "^2.8.0", "@types/express": "^4.17.0", "@types/jest": "^29.0.0", "@types/morgan": "^1.9.0", "@types/node": "^20.0.0", "@types/supertest": "^6.0.0", "@types/winston": "^2.4.4", "eslint": "^8.0.0", "jest": "^29.0.0", "supertest": "^6.0.0", "ts-jest": "^29.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}}