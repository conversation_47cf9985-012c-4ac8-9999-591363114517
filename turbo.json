{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "build/**"]}, "lint": {"outputs": []}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"]}}}