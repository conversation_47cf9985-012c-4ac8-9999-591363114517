import { Router, Request, Response } from 'express';
import { MoneyGramService, TransactionUpdate } from '../services/moneygram.service';
import { QuoteRequest } from '../models/quote.request';
import logger from '../config/logger';
import { v4 as uuidv4 } from 'uuid';

const router: Router = Router();
const moneyGramService = new MoneyGramService();

// Debug log to show routes are loaded
console.log('Initializing transaction routes...');

// Get currencies
router.get('/currencies', async (req: Request, res: Response) => {
  console.log('Currencies endpoint called');
  try {
    console.log('Getting currencies...');
    const currencies = await moneyGramService.getCurrencies();
    res.json(currencies);
  } catch (error: any) {
    console.error('Error getting currencies:', error);
    res.status(500).json({ 
      error: [{ 
        category: 'API_ERROR', 
        code: '500', 
        message: error.message || 'Failed to fetch currencies' 
      }] 
    });
  }
});

// Get quote for a transaction
router.post('/quote', async (req: Request, res: Response) => {
  const requestId = uuidv4();
  
  logger.info('=== Quote Request Started ===', {
    requestId,
    timestamp: new Date().toISOString(),
    method: 'POST',
    path: '/api/transactional/quote',
    clientIp: req.ip,
    userAgent: req.get('user-agent'),
    requestBody: {
      ...req.body,
      // Mask any sensitive data
      agentPartnerId: '****',
      rewardsNumber: req.body.rewardsNumber ? '****' : undefined
    },
    headers: {
      ...req.headers,
      authorization: req.headers.authorization ? '****' : undefined,
      cookie: req.headers.cookie ? '****' : undefined
    }
  });

  try {
    const quoteRequest: QuoteRequest = req.body;
    const startTime = Date.now();
    const quote = await moneyGramService.getQuote(quoteRequest);
    const duration = Date.now() - startTime;
    
    logger.info('=== Quote Response Success ===', {
      requestId,
      timestamp: new Date().toISOString(),
      duration: `${duration}ms`,
      status: 200,
      responseBody: {
        transactionCount: quote.transactions?.length || 0,
        transactions: quote.transactions?.map(t => ({
          transactionId: t.transactionId,
          serviceOptionCode: t.serviceOptionCode,
          serviceOptionName: t.serviceOptionName,
          estimatedDelivery: t.estimatedDelivery,
          sendAmount: {
            amount: t.sendAmount.amount,
            fees: t.sendAmount.fees,
            total: t.sendAmount.total
          },
          receiveAmount: {
            amount: t.receiveAmount.amount,
            fxRate: t.receiveAmount.fxRate,
            total: t.receiveAmount.total
          }
        }))
      }
    });

    res.json(quote);
  } catch (error: any) {
    logger.error('=== Quote Error ===', {
      requestId,
      timestamp: new Date().toISOString(),
      status: error.status || 500,
      errorDetails: {
        message: error.message,
        code: error.code,
        response: error.response?.data,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    });

    // If error is already in the correct format, pass it through
    if (error.error && typeof error.error === 'object' && !Array.isArray(error.error)) {
      return res.status(error.status || 500).json(error);
    }

    // Transform to the correct format
    res.status(error.status || 500).json({
      error: {
        category: 'API_ERROR',
        code: String(error.status || 500),
        message: error.message || 'Failed to get quote',
        offendingFields: error.offendingFields || []
      }
    });
  } finally {
    logger.info('=== Quote Request Completed ===', {
      requestId,
      timestamp: new Date().toISOString()
    });
  }
});

// Update transaction details
router.put('/:transactionId', async (req: Request, res: Response) => {
  console.log('Update transaction endpoint called');
  try {
    const { transactionId } = req.params;
    const updateData: TransactionUpdate = req.body;
    const updatedTransaction = await moneyGramService.updateTransaction(transactionId, updateData);
    res.json(updatedTransaction);
  } catch (error: any) {
    res.status(error.status || 500).json(error);
  }
});

// Commit transaction
router.put('/:transactionId/commit', async (req: Request, res: Response) => {
  console.log('Commit transaction endpoint called');
  try {
    const { transactionId } = req.params;
    const committedTransaction = await moneyGramService.commitTransaction(transactionId);
    res.json(committedTransaction);
  } catch (error: any) {
    res.status(error.status || 500).json(error);
  }
});

export default router; 