import { Device, DeviceSchema } from '@agentworks/utils';
import logger from '../config/logger';

// Mock database
const mockDevices: Device[] = [
  {
    deviceName: 'Terminal 001',
    deviceId: 'DEV001',
    deviceStatus: 'active',
    deviceSecurityLevel: 'high',
    deviceType: 'pos'
  }
];

export const deviceService = {
  async getDevice(deviceId: string): Promise<Device> {
    logger.info(`Fetching device with ID: ${deviceId}`);
    const device = mockDevices.find(d => d.deviceId === deviceId);
    if (!device) {
      throw new Error('Device not found');
    }
    return device;
  },

  async createDevice(deviceData: Device): Promise<Device> {
    logger.info('Creating new device');
    const validatedDevice = DeviceSchema.parse(deviceData);
    mockDevices.push(validatedDevice);
    return validatedDevice;
  },

  async updateDevice(deviceId: string, deviceData: Partial<Device>): Promise<Device> {
    logger.info(`Updating device with ID: ${deviceId}`);
    const deviceIndex = mockDevices.findIndex(d => d.deviceId === deviceId);
    if (deviceIndex === -1) {
      throw new Error('Device not found');
    }

    const updatedDevice = {
      ...mockDevices[deviceIndex],
      ...deviceData
    };

    DeviceSchema.parse(updatedDevice);
    mockDevices[deviceIndex] = updatedDevice;
    return updatedDevice;
  }
}; 