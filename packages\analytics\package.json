{"name": "@agentworks/analytics", "version": "0.0.1", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts", "dev": "tsup src/index.ts --format cjs,esm --watch --dts", "lint": "eslint \"src/**/*.ts\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"posthog-js": "^1.242.2"}, "devDependencies": {"@agentworks/configs": "workspace:*", "eslint": "^8.0.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}}