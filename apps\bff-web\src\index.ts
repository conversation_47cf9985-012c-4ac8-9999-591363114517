import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import { config } from './config';
import transactionRoutes from './routes/transaction.routes';
import feeQuoteRoutes from './routes/fee-quote.routes';
import consumerRoutes from './routes/consumer.routes';
import logger from './config/logger';

const app = express();
const port = config.port;

// Middleware
app.use(cors({
  origin: config.cors.origin,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'Accept'],
  credentials: true,
  maxAge: 86400 // 24 hours
}));

// Preflight OPTIONS handler
app.options('*', cors());

app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());

// Routes
app.use('/api/transactional', transactionRoutes);
app.use('/api/fee-quote', feeQuoteRoutes);
app.use('/api/consumer', consumerRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error(err.stack);
  res.status(500).json({
    message: 'Internal Server Error',
    error: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Start server
app.listen(port, () => {
  logger.info(`BFF Web service listening at http://localhost:${port}`);
}); 