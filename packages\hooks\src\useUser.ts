import { useQuery, useMutation } from '@tanstack/react-query';
import axios from 'axios';
import type { User } from '@agentworks/utils';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';

export const useUser = (userId: string) => {
  return useQuery({
    queryKey: ['user', userId],
    queryFn: async () => {
      const { data } = await axios.get<User>(`${API_URL}/api/mobile/user-data/${userId}`);
      return data;
    }
  });
};

export const useUpdateUser = () => {
  return useMutation({
    mutationFn: async ({ userId, userData }: { userId: string; userData: Partial<User> }) => {
      const { data } = await axios.post(`${API_URL}/api/mobile/batch-update`, {
        userId,
        updates: { user: userData }
      });
      return data;
    }
  });
}; 