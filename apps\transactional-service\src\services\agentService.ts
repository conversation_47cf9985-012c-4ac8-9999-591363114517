import { Agent, AgentSchema } from '@agentworks/utils';
import logger from '../config/logger';

// Mock database
const mockAgents: Agent[] = [
  {
    agentName: 'Main Street Agency',
    agentAddress: '123 Main St',
    agentCity: 'Dallas',
    agentState: 'TX',
    agentCountry: 'USA',
    agentStatus: 'active'
  }
];

export const agentService = {
  async getAgent(agentId: string): Promise<Agent> {
    logger.info(`Fetching agent with ID: ${agentId}`);
    const agent = mockAgents[0]; // Mock implementation
    if (!agent) {
      throw new Error('Agent not found');
    }
    return agent;
  },

  async createAgent(agentData: Agent): Promise<Agent> {
    logger.info('Creating new agent');
    const validatedAgent = AgentSchema.parse(agentData);
    mockAgents.push(validatedAgent);
    return validatedAgent;
  },

  async updateAgent(agentId: string, agentData: Partial<Agent>): Promise<Agent> {
    logger.info(`Updating agent with ID: ${agentId}`);
    const agentIndex = 0; // Mock implementation
    
    const updatedAgent = {
      ...mockAgents[agentIndex],
      ...agentData
    };

    AgentSchema.parse(updatedAgent);
    mockAgents[agentIndex] = updatedAgent;
    return updatedAgent;
  }
}; 