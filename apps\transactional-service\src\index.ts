// Load environment variables first
import { config } from 'dotenv';
config();

// Handle self-signed certificates in development
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import logger from './config/logger';
import transactionRoutes from './routes/transaction.routes';
import feeQuoteRoutes from './routes/fee-quote.routes';
import consumerRoutes from './routes/consumer.routes';

const app = express();
const port = process.env.PORT || 3000;

// Log environment state
logger.info('Environment:', {
  NODE_ENV: process.env.NODE_ENV || 'not set',
  PORT: port
});

// Request logging middleware
app.use((req, res, next) => {
  logger.info(`${req.method} ${req.url}`);
  next();
});

// Middleware
app.use(cors());
app.use(helmet());
app.use(morgan('dev'));
app.use(express.json());

// Mount MoneyGram routes
app.use('/api/transactional', transactionRoutes);
app.use('/api/fee-quote', feeQuoteRoutes);
app.use('/api/consumer', consumerRoutes);

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'ok' });
});

// Error handling middleware
app.use((err: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
  logger.error(err.stack);
  res.status(500).json({
    error: [{
      category: 'SERVER_ERROR',
      code: '500',
      message: 'Internal server error'
    }]
  });
});

// 404 handler
app.use((req, res) => {
  logger.warn(`Route not found: ${req.method} ${req.url}`);
  res.status(404).json({ error: `Route not found: ${req.method} ${req.url}` });
});

// Start server
app.listen(port, () => {
  logger.info(`Transactional service listening at http://localhost:${port}`);
  logger.info('Available routes:');
  logger.info('  GET  /api/transactional/currencies');
  logger.info('  POST /api/transactional/quote');
  logger.info('  PUT  /api/transactional/:transactionId');
  logger.info('  PUT  /api/transactional/:transactionId/commit');
  logger.info('  GET  /api/consumer/search');
  logger.info('  GET  /health');
}); 