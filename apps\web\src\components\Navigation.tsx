import Link from 'next/link'
import { View, StyleSheet } from 'react-native-web'

const Navigation = () => {
  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        <View style={styles.content}>
          <View style={styles.logoContainer}>
            {/* Logo */}
          </View>
          <View style={styles.navLinks}>
            <Link href="/money-transfer/send-money" style={styles.link}>
              Send Money
            </Link>
          </View>
        </View>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1a202c',
  },
  innerContainer: {
    maxWidth: 1280,
    marginHorizontal: 'auto',
    paddingHorizontal: 16,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    height: 64,
  },
  logoContainer: {
    flexShrink: 0,
    height: 32,
    width: 'auto',
  },
  navLinks: {
    marginLeft: 40,
    flexDirection: 'row',
    gap: 16,
  },
  link: {
    color: '#cbd5e0',
    fontSize: 14,
    fontWeight: '500',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 6,
  },
});

export default Navigation