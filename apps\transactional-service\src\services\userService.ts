import { User, UserSchema } from '@agentworks/utils';
import logger from '../config/logger';

// Mock database - In a real app, this would be a database connection
const mockUsers: User[] = [
  {
    firstName: '<PERSON>',
    lastName: 'Doe',
    userId: '1',
    emailId: '<EMAIL>',
    agentId: 'AGT001',
    mainOfficeId: 'OFF001',
    hqAccountNo: 'HQ001',
    country: 'USA',
    preferredLanguage: 'en'
  }
];

export const userService = {
  async getUser(userId: string): Promise<User> {
    logger.info(`Fetching user with ID: ${userId}`);
    const user = mockUsers.find(u => u.userId === userId);
    if (!user) {
      throw new Error('User not found');
    }
    return user;
  },

  async createUser(userData: User): Promise<User> {
    logger.info('Creating new user');
    const validatedUser = UserSchema.parse(userData);
    mockUsers.push(validatedUser);
    return validatedUser;
  },

  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    logger.info(`Updating user with ID: ${userId}`);
    const userIndex = mockUsers.findIndex(u => u.userId === userId);
    if (userIndex === -1) {
      throw new Error('User not found');
    }

    const updatedUser = {
      ...mockUsers[userIndex],
      ...userData
    };

    UserSchema.parse(updatedUser); // Validate the updated user
    mockUsers[userIndex] = updatedUser;
    return updatedUser;
  }
}; 