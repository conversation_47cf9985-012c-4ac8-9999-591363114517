import { Request, Response } from 'express';
import { ConsumerService } from '../services/consumer.service';
import { MoneyGramConsumerService } from '../services/moneygram-consumer.service';
import { SearchParams } from '../models/consumer.model';

export class ConsumerController {
  private consumerService: ConsumerService;
  private moneyGramService: MoneyGramConsumerService;

  constructor() {
    this.consumerService = new ConsumerService();
    this.moneyGramService = new MoneyGramConsumerService();
  }

  async search(req: Request, res: Response) {
    try {
      const searchParams: SearchParams = {
        userLanguage: req.query.userLanguage as string,
        rewardsNumber: req.query.rewardsNumber as string,
        identificationType: req.query.identificationType as string,
        identificationNumber: req.query.identificationNumber as string,
        mobilePhone: req.query.mobilePhone as string,
        maxProfilesToReturn: req.query.maxProfilesToReturn ? parseInt(req.query.maxProfilesToReturn as string) : undefined,
        pageNumber: req.query.pageNumber ? parseInt(req.query.pageNumber as string) : undefined,
        perPage: req.query.perPage ? parseInt(req.query.perPage as string) : undefined
      };

      const result = await this.consumerService.search(searchParams);
      res.json(result);
    } catch (error: any) {
      console.error('Error in consumer search:', error);

      // If error is already in the MoneyGram format, pass it through
      if (error.error && typeof error.error === 'object' && !Array.isArray(error.error)) {
        return res.status(error.status || 500).json(error);
      }

      // Transform to the correct format for other errors
      res.status(500).json({
        error: {
          category: 'API_ERROR',
          code: '500',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          offendingFields: []
        }
      });
    }
  }

  // Debug endpoint to test URL construction
  async debugUrl(req: Request, res: Response) {
    try {
      const searchParams: SearchParams = {
        userLanguage: req.query.userLanguage as string,
        rewardsNumber: req.query.rewardsNumber as string,
        identificationType: req.query.identificationType as string,
        identificationNumber: req.query.identificationNumber as string,
        mobilePhone: req.query.mobilePhone as string,
        maxProfilesToReturn: req.query.maxProfilesToReturn ? parseInt(req.query.maxProfilesToReturn as string) : undefined,
        pageNumber: req.query.pageNumber ? parseInt(req.query.pageNumber as string) : undefined,
        perPage: req.query.perPage ? parseInt(req.query.perPage as string) : undefined
      };

      const constructedUrl = this.moneyGramService.constructSearchUrl(searchParams);

      res.json({
        constructedUrl,
        searchParams,
        examples: {
          driverLicense: 'https://extintsvcs.aws.moneygram.com/searchconsumerprofileservice/profiles/search?agentPartnerId=30164505&userLanguage=en-US&identificationType=Drivers%20License&identificationNumber=**********&maxProfilesToReturn=10&pageNumber=1&perPage=10',
          mobilePhone: 'https://extintsvcs.aws.moneygram.com/searchconsumerprofileservice/profiles/search?agentPartnerId=30164505&userLanguage=en-US&mobilePhone=**********&maxProfilesToReturn=10&pageNumber=1&perPage=10',
          rewardsNumber: 'https://extintsvcs.aws.moneygram.com/searchconsumerprofileservice/profiles/search?agentPartnerId=30164505&userLanguage=en-US&rewardsNumber=251110955431&maxProfilesToReturn=10&pageNumber=1&perPage=10'
        }
      });
    } catch (error: any) {
      console.error('Error in URL debug:', error);
      res.status(500).json({
        error: {
          category: 'API_ERROR',
          code: '500',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
          offendingFields: []
        }
      });
    }
  }
}