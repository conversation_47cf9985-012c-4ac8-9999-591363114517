import axios, { AxiosInstance } from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { SearchParams, SearchResponse, Profile } from '../models/consumer.model';
import https from 'https';
import logger from '../config/logger';
import { config } from '../config';

// MoneyGram API response interfaces
interface MoneyGramProfile {
  name: {
    firstName: string;
    lastName: string;
  };
  address: {
    line1: string;
    city: string;
    countrySubdivisionCode: string;
    countryCode: string;
    postalCode: string;
  };
  mobilePhone: {
    number: string;
    countryDialCode: string;
  };
  rewardsNumber?: string;
  dateofBirth?: string;
  profileId: string;
}

interface MoneyGramSearchResponse {
  profiles: MoneyGramProfile[];
  paginationMetadata: {
    totalItems: number;
    currentPage: number;
    perPage: number;
    totalPages: number;
  };
}

export class MoneyGramConsumerService {
  private client: AxiosInstance;

  constructor() {
    // Configure axios with HTTPS agent for development
    const httpsAgent = process.env.NODE_ENV === 'development'
      ? new https.Agent({ rejectUnauthorized: false })
      : undefined;

    this.client = axios.create({
      baseURL: config.moneyGram.coreServiceUrl.replace(/\/$/, ''),
      httpsAgent,
      timeout: 10000
    });
  }

  // Helper method to construct and return the URL for debugging
  constructSearchUrl(params: SearchParams): string {
    const searchParams = new URLSearchParams();

    const partnerId = config.moneyGram.partnerId;
    searchParams.append('agentPartnerId', partnerId);
    searchParams.append('userLanguage', params.userLanguage || 'en-US');
    searchParams.append('maxProfilesToReturn', String(params.maxProfilesToReturn || 10));
    searchParams.append('pageNumber', String(params.pageNumber || 1));
    searchParams.append('perPage', String(params.perPage || 10));

    if (params.identificationType && params.identificationNumber) {
      const normalizedIdType = this.normalizeIdentificationType(params.identificationType);
      searchParams.append('identificationType', normalizedIdType);
      searchParams.append('identificationNumber', params.identificationNumber);
    }

    if (params.mobilePhone) {
      searchParams.append('mobilePhone', params.mobilePhone);
    }

    if (params.rewardsNumber) {
      searchParams.append('rewardsNumber', params.rewardsNumber);
    }

    const url = `/searchconsumerprofileservice/profiles/search?${searchParams.toString()}`;
    return `${this.client.defaults.baseURL}${url}`;
  }

  async searchProfiles(params: SearchParams): Promise<SearchResponse> {
    try {
      const searchParams = new URLSearchParams();

      // Required parameters
      const partnerId = config.moneyGram.partnerId;
      searchParams.append('agentPartnerId', partnerId);
      searchParams.append('userLanguage', params.userLanguage || 'en-US');
      searchParams.append('maxProfilesToReturn', String(params.maxProfilesToReturn || 10));
      searchParams.append('pageNumber', String(params.pageNumber || 1));
      searchParams.append('perPage', String(params.perPage || 10));

      // Search criteria - add based on what's provided
      if (params.identificationType && params.identificationNumber) {
        searchParams.append('identificationType', params.identificationType);
        searchParams.append('identificationNumber', params.identificationNumber);
      }

      if (params.mobilePhone) {
        searchParams.append('mobilePhone', params.mobilePhone);
      }

      if (params.rewardsNumber) {
        searchParams.append('rewardsNumber', params.rewardsNumber);
      }

      const url = `/searchconsumerprofileservice/profiles/search?${searchParams.toString()}`;
      const fullUrl = `${this.client.defaults.baseURL}${url}`;

      logger.info('=== MoneyGram Consumer Search Request ===', {
        url,
        fullUrl,
        params: Object.fromEntries(searchParams.entries()),
        rawQueryString: searchParams.toString(),
        originalParams: params,
        partnerId,
        baseURL: this.client.defaults.baseURL,
        timestamp: new Date().toISOString()
      });

      const response = await this.client.get<MoneyGramSearchResponse>(url, {
        headers: {
          'accept': 'application/json',
          'X-MG-ClientRequestId': uuidv4(),
          'X-MG-RequestId': uuidv4(),
          'Content-Type': 'application/json'
        }
      });

      logger.info('=== MoneyGram Consumer Search Response ===', {
        status: response.status,
        profileCount: response.data.profiles?.length || 0,
        totalItems: response.data.paginationMetadata?.totalItems || 0,
        timestamp: new Date().toISOString()
      });

      // Transform MoneyGram response to our format
      const transformedProfiles: Profile[] = response.data.profiles.map(profile => ({
        name: {
          firstName: profile.name.firstName,
          lastName: profile.name.lastName
        },
        address: {
          line1: profile.address.line1,
          city: profile.address.city,
          countrySubdivisionCode: profile.address.countrySubdivisionCode,
          countryCode: profile.address.countryCode,
          postalCode: profile.address.postalCode
        },
        mobilePhone: {
          number: profile.mobilePhone.number,
          countryDialCode: profile.mobilePhone.countryDialCode
        },
        rewardsNumber: profile.rewardsNumber,
        dateofBirth: profile.dateofBirth,
        profileId: profile.profileId
      }));

      return {
        profiles: transformedProfiles,
        paginationMetadata: {
          totalItems: response.data.paginationMetadata.totalItems,
          currentPage: response.data.paginationMetadata.currentPage,
          perPage: response.data.paginationMetadata.perPage,
          totalPages: response.data.paginationMetadata.totalPages
        }
      };

    } catch (error: any) {
      logger.error('=== MoneyGram Consumer Search Error ===', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        message: error.message,
        response: error.response?.data,
        headers: error.response?.headers,
        config: {
          url: error.config?.url,
          method: error.config?.method,
          baseURL: error.config?.baseURL,
          params: error.config?.params
        },
        timestamp: new Date().toISOString()
      });

      throw this.handleError(error);
    }
  }

  private handleError(error: any): any {
    // Handle network errors or cases where response is undefined
    if (!error.response) {
      return {
        error: {
          category: 'NETWORK_ERROR',
          code: 'CONNECTION_ERROR',
          message: error.message || 'Network error occurred while searching consumer profiles',
          offendingFields: []
        }
      };
    }

    // Transform to the desired format
    return {
      error: {
        category: error.response.status ? 'API_ERROR' : 'SYSTEM_ERROR',
        code: String(error.response.status || 'UNKNOWN'),
        message: error.response.data?.message || error.message || 'Failed to search consumer profiles',
        offendingFields: error.response.data?.offendingFields?.map((field: any) => ({
          field: field.field || field
        })) || []
      }
    };
  }
}
