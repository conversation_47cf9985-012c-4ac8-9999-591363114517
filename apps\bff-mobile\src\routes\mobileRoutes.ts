import { Router } from 'express';
import as<PERSON><PERSON>and<PERSON> from 'express-async-handler';
import { transactionalServiceClient } from '../clients/transactionalServiceClient';

const router = Router();

// Get all user-related data in a single request
router.get('/user-data/:userId', asyncHandler(async (req, res) => {
  const userId = req.params.userId;
  const [user, agent, devices, profile] = await Promise.all([
    transactionalServiceClient.getUser(userId),
    transactionalServiceClient.getAgent(userId), // Assuming agentId is the same as userId
    Promise.resolve([]), // TODO: Get user's devices
    transactionalServiceClient.getProfile(userId) // Assuming profileId is the same as userId
  ]);

  res.json({
    user,
    agent,
    devices,
    profile
  });
}));

// Batch update endpoint for mobile
router.post('/batch-update', asyncHandler(async (req, res) => {
  const { userId, updates } = req.body;
  const results = {
    user: null,
    agent: null,
    device: null,
    profile: null
  };

  if (updates.user) {
    results.user = await transactionalServiceClient.updateUser(userId, updates.user);
  }

  if (updates.agent) {
    results.agent = await transactionalServiceClient.updateAgent(userId, updates.agent);
  }

  if (updates.device) {
    results.device = await transactionalServiceClient.updateDevice(userId, updates.device);
  }

  if (updates.profile) {
    results.profile = await transactionalServiceClient.updateProfile(userId, updates.profile);
  }

  res.json(results);
}));

// Offline sync endpoint
router.post('/sync', asyncHandler(async (req, res) => {
  const { userId, offlineChanges } = req.body;
  const syncResults = [];

  for (const change of offlineChanges) {
    try {
      let result;
      switch (change.type) {
        case 'user':
          result = await transactionalServiceClient.updateUser(userId, change.data);
          break;
        case 'agent':
          result = await transactionalServiceClient.updateAgent(userId, change.data);
          break;
        case 'device':
          result = await transactionalServiceClient.updateDevice(userId, change.data);
          break;
        case 'profile':
          result = await transactionalServiceClient.updateProfile(userId, change.data);
          break;
      }
      syncResults.push({ success: true, change, result });
    } catch (error) {
      syncResults.push({ success: false, change, error: error.message });
    }
  }

  res.json({
    synced: syncResults
  });
}));

export default router; 