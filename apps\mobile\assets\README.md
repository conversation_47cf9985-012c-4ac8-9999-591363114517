# Assets Directory

This directory should contain the following image files for the mobile app:

## Required Assets:

1. **icon.png** - App icon (1024x1024 px recommended)
2. **splash.png** - Splash screen image 
3. **adaptive-icon.png** - Android adaptive icon foreground (1024x1024 px)
4. **favicon.png** - Web favicon (32x32 px)

## Instructions:

1. Replace these placeholder files with actual image assets
2. Ensure proper dimensions for each asset type
3. Use PNG format for best compatibility
4. Consider using Expo's asset generation tools: `npx expo install expo-splash-screen`

## Temporary Setup:

For now, you can use any placeholder images with the correct names to test the app.
The app will work without these assets, but you'll see warnings in the console.
