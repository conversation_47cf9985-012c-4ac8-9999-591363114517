import axios from 'axios';
import { config } from '../config';

export interface OAuthResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
  issued_at: string;
  status: string;
}

export class AuthService {
  private static instance: AuthService;
  private tokenData: {
    accessToken: string;
    expiresAt: number;
  } | null = null;

  private constructor() {}

  public static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  public async getAccessToken(): Promise<string> {
    const now = Date.now();
    
    // Check if we have a valid cached token
    if (this.tokenData && this.tokenData.expiresAt > now) {
      console.log('Using cached token, expires in:', Math.round((this.tokenData.expiresAt - now) / 1000), 'seconds');
      return this.tokenData.accessToken;
    }

    if (this.tokenData) {
      console.log('Token expired, last expiry was:', new Date(this.tokenData.expiresAt).toISOString());
    } else {
      console.log('No token found, fetching new token');
    }

    try {
      // Log configuration values (mask sensitive data)
      console.log('MoneyGram Configuration:', {
        baseUrl: config.moneyGram.baseUrl,
        apiBaseUrl: config.moneyGram.apiBaseUrl,
        clientId: config.moneyGram.clientId ? '****' + config.moneyGram.clientId.slice(-4) : 'NOT_SET',
        clientSecret: config.moneyGram.clientSecret ? '****' : 'NOT_SET',
        partnerId: config.moneyGram.partnerId ? '****' + config.moneyGram.partnerId.slice(-4) : 'NOT_SET',
        environment: config.moneyGram.environment
      });

      const credentials = Buffer.from(
        `${config.moneyGram.clientId}:${config.moneyGram.clientSecret}`
      ).toString('base64');

      console.log('Fetching new access token...');
      
      const response = await axios.get<OAuthResponse>(
        `${config.moneyGram.baseUrl}/oauth/accesstoken?grant_type=client_credentials`,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${credentials}`,
          },
        }
      );

      console.log('Access token response status:', response.status);
      console.log('Token expires in:', response.data.expires_in, 'seconds');

      // Store the token with expiration
      const expiresAt = now + (response.data.expires_in * 1000) - (5 * 60 * 1000); // 5 minutes safety margin
      this.tokenData = {
        accessToken: response.data.access_token,
        expiresAt
      };

      console.log('Token cached, will expire at:', new Date(expiresAt).toISOString());

      return this.tokenData.accessToken;
    } catch (error: any) {
      console.error('Failed to get access token. Error details:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        headers: error.response?.headers,
        requestUrl: error.config?.url,
        requestHeaders: {
          ...error.config?.headers,
          'Authorization': '****' // Mask the actual token
        }
      });
      
      if (!config.moneyGram.clientId || !config.moneyGram.clientSecret) {
        console.error('Missing required credentials in configuration');
      }
      
      throw new Error('Failed to obtain access token');
    }
  }

  public clearToken(reason: string = 'manual'): void {
    console.log(`Clearing cached token. Reason: ${reason}`);
    this.tokenData = null;
  }
} 