/** @type {import('next').NextConfig} */

// <PERSON><PERSON> self-signed certificates in development
if (process.env.NODE_ENV === 'development') {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
}

const nextConfig = {
  webpack: (config) => {
    // Handle react-native-web aliases
    config.resolve.alias = {
      ...(config.resolve.alias || {}),
      'react-native$': 'react-native-web',
      'react-native-web$': 'react-native-web',
    }

    // Ensure proper handling of react-native-web modules
    config.resolve.extensions = [
      '.web.js',
      '.web.jsx',
      '.web.ts',
      '.web.tsx',
      ...config.resolve.extensions || ['.js', '.jsx', '.ts', '.tsx']
    ]

    // Configure performance budgets and hints
    config.performance = {
      hints: false,
      maxEntrypointSize: 512000,
      maxAssetSize: 512000
    }

    // Configure node polyfills
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
      crypto: false,
      stream: false,
      zlib: false
    }

    return config
  },
  transpilePackages: [
    'react-native',
    'react-native-web',
    '@agentworks/ui',
    '@agentworks/hooks',
    '@agentworks/utils'
  ],
  // Simplified configuration to reduce memory usage
  experimental: {
    esmExternals: true
  },
  swcMinify: false, // Disable minification temporarily
  poweredByHeader: false,
  reactStrictMode: true,
  compress: false, // Disable compression
  // Configure memory-sensitive features
  onDemandEntries: {
    maxInactiveAge: 60 * 1000,
    pagesBufferLength: 1
  },
  // Add API rewrites to proxy requests to BFF-web
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: 'http://localhost:3002/api/:path*'
      }
    ]
  }
}

module.exports = nextConfig 