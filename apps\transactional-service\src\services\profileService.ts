import { Profile, ProfileSchema } from '@agentworks/utils';
import logger from '../config/logger';

// Mock database
const mockProfiles: Profile[] = [
  {
    profileId: 'PRF001',
    profileNo: 'PROFILE-001',
    profileStatus: 'active',
    profileType: 'agent'
  }
];

export const profileService = {
  async getProfile(profileId: string): Promise<Profile> {
    logger.info(`Fetching profile with ID: ${profileId}`);
    const profile = mockProfiles.find(p => p.profileId === profileId);
    if (!profile) {
      throw new Error('Profile not found');
    }
    return profile;
  },

  async createProfile(profileData: Profile): Promise<Profile> {
    logger.info('Creating new profile');
    const validatedProfile = ProfileSchema.parse(profileData);
    mockProfiles.push(validatedProfile);
    return validatedProfile;
  },

  async updateProfile(profileId: string, profileData: Partial<Profile>): Promise<Profile> {
    logger.info(`Updating profile with ID: ${profileId}`);
    const profileIndex = mockProfiles.findIndex(p => p.profileId === profileId);
    if (profileIndex === -1) {
      throw new Error('Profile not found');
    }

    const updatedProfile = {
      ...mockProfiles[profileIndex],
      ...profileData
    };

    ProfileSchema.parse(updatedProfile);
    mockProfiles[profileIndex] = updatedProfile;
    return updatedProfile;
  }
}; 