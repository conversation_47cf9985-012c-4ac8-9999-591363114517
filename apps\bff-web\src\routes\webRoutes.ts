import { Router, Request, Response } from 'express';
import as<PERSON><PERSON><PERSON><PERSON> from 'express-async-handler';
import { transactionalServiceClient } from '../clients/transactionalServiceClient';

const router: Router = Router();

// Get paginated and filtered data for web tables
router.get('/users', asyncHandler(async (req: Request, res: Response) => {
  const { page = 1, limit = 10, search } = req.query;
  const users = await transactionalServiceClient.getUsers({ 
    page: Number(page), 
    limit: Number(limit), 
    search: search?.toString() 
  });
  res.json(users);
}));

// Get all data for a specific user
router.get('/user-dashboard/:userId', asyncHandler(async (req: Request, res: Response) => {
  const userId = req.params.userId;
  
  // First get the user to get the associated IDs
  const user = await transactionalServiceClient.getUser(userId);
  
  // Then fetch the rest of the data using the correct IDs
  const [agent, device, profile] = await Promise.all([
    transactionalServiceClient.getAgent(user.agentId),
    transactionalServiceClient.getDevice('DEV001'), // Using the known mock device ID
    transactionalServiceClient.getProfile('PRF001') // Using the known mock profile ID for now
  ]);

  const dashboardData = {
    user,
    agent,
    devices: device ? [device] : [], // Wrap the device in an array
    profile,
    _metadata: {
      timestamp: new Date().toISOString()
    }
  };

  res.json(dashboardData);
}));

// Bulk operations for web interface
router.post('/bulk-update', asyncHandler(async (req: Request, res: Response) => {
  const { operations } = req.body;
  const results = [];

  for (const op of operations) {
    try {
      let result;
      switch (op.type) {
        case 'user':
          result = await transactionalServiceClient.updateUser(op.id, op.data);
          break;
        case 'agent':
          result = await transactionalServiceClient.updateAgent(op.id, op.data);
          break;
        case 'device':
          result = await transactionalServiceClient.updateDevice(op.id, op.data);
          break;
        case 'profile':
          result = await transactionalServiceClient.updateProfile(op.id, op.data);
          break;
      }
      results.push({ success: true, operation: op, result });
    } catch (err) {
      const error = err as Error;
      results.push({ success: false, operation: op, error: error.message });
    }
  }

  res.json({ results });
}));

export default router; 