{"name": "@agentworks/ui", "version": "0.1.0", "private": true, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "sideEffects": false, "files": ["dist/**"], "scripts": {"build": "tsup src/index.ts --format cjs,esm --dts --external react", "dev": "tsup src/index.ts --format cjs,esm --watch --dts --external react", "lint": "eslint \"src/**/*.{ts,tsx}\"", "clean": "rm -rf .turbo && rm -rf node_modules && rm -rf dist"}, "dependencies": {"@agentworks/utils": "workspace:*", "@react-native-picker/picker": "^2.11.0", "nativewind": "^2.0.0", "react": "^18.2.0", "react-native": "^0.72.0", "react-native-web": "^0.19.0"}, "devDependencies": {"@agentworks/configs": "workspace:*", "@types/react": "^18.2.0", "@types/react-dom": "^18.0.0", "@types/react-native": "^0.72.0", "eslint": "^8.0.0", "react-dom": "^18.0.0", "tailwindcss": "^3.3.0", "tsup": "^8.0.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.0.0", "react-native": "^0.72.0", "react-native-web": "^0.19.0"}}