# Dependencies
node_modules
.pnp
.pnp.js

# Testing
coverage

# Build outputs
.next/
out/
build
dist
.turbo

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE and editor files
.idea
.vscode/*
!.vscode/extensions.json
!.vscode/launch.json
!.vscode/settings.json
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS files
.DS_Store
Thumbs.db
