{"extends": "@agentworks/configs/tsconfig.base.json", "compilerOptions": {"target": "ES2019", "lib": ["DOM", "DOM.Iterable", "ESNext"], "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "esModuleInterop": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "paths": {"@/*": ["./*"]}}, "include": ["src/**/*", "expo/AppEntry.js", "index.js", "src/types/**/*"], "exclude": ["node_modules", "dist"]}