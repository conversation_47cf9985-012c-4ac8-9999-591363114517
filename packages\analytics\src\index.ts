import { PostHog } from 'posthog-js';

// Initialize PostHog instance
let posthogInstance: PostHog | null = null;

// Configuration interface
interface AnalyticsConfig {
  apiKey?: string;
  apiHost?: string;
  disabled?: boolean;
}

// Initialize analytics
export const initAnalytics = (config: AnalyticsConfig = {}) => {
  // Skip initialization if disabled or in test environment
  if (config.disabled || process.env.NODE_ENV === 'test') {
    return;
  }

  // Only initialize in browser environment
  if (typeof window !== 'undefined') {
    try {
      const { default: posthog } = require('posthog-js');
      
      const apiKey = config.apiKey || process.env.NEXT_PUBLIC_POSTHOG_KEY || process.env.REACT_APP_POSTHOG_KEY;
      const apiHost = config.apiHost || process.env.NEXT_PUBLIC_POSTHOG_HOST || process.env.REACT_APP_POSTHOG_HOST || 'https://app.posthog.com';

      if (apiKey) {
        posthog.init(apiKey, {
          api_host: apiHost,
          loaded: (posthog: PostHog) => {
            posthogInstance = posthog;
          }
        });
      }
    } catch (error) {
      console.warn('Failed to initialize analytics:', error);
    }
  }
};

// Create a proxy object that safely handles PostHog calls
export const posthog = {
  capture: (event: string, properties?: Record<string, any>) => {
    if (posthogInstance && typeof window !== 'undefined') {
      try {
        posthogInstance.capture(event, properties);
      } catch (error) {
        console.warn('Analytics capture failed:', error);
      }
    }
  },
  
  identify: (distinctId: string, properties?: Record<string, any>) => {
    if (posthogInstance && typeof window !== 'undefined') {
      try {
        posthogInstance.identify(distinctId, properties);
      } catch (error) {
        console.warn('Analytics identify failed:', error);
      }
    }
  },
  
  reset: () => {
    if (posthogInstance && typeof window !== 'undefined') {
      try {
        posthogInstance.reset();
      } catch (error) {
        console.warn('Analytics reset failed:', error);
      }
    }
  }
};

// Export types
export type { AnalyticsConfig };
