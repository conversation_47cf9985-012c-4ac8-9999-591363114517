import axios from 'axios';
import { config } from '../config';

export interface ConsumerSearchParams {
  userLanguage?: string;
  rewardsNumber?: string;
  identificationType?: string;
  identificationNumber?: string;
  mobilePhone?: string;
  maxProfilesToReturn?: number;
  pageNumber?: number;
  perPage?: number;
}

export interface Name {
  firstName: string;
  middleName?: string;
  lastName: string;
  secondLastName?: string;
}

export interface Address {
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  countrySubdivision: string;
  countryCode: string;
  postalCode: string;
}

export interface MobilePhone {
  number: string;
  countryDialCode: string;
}

export interface Profile {
  name: Name;
  address: Address;
  mobilePhone: MobilePhone;
  rewardsNumber?: string;
  profileId: string;
}

export interface PaginationLink {
  pageNumber: number;
  href: string;
}

export interface PaginationMetadata {
  totalItems: number;
  currentPage: number;
  perPage: number;
  totalPages: number;
  links: PaginationLink[];
}

export interface SearchResponse {
  profiles: Profile[];
  paginationMetadata: PaginationMetadata;
}

export class ConsumerClient {
  private baseUrl: string;

  constructor() {
    this.baseUrl = `${config.services.transactional.url}/api/consumer`;
  }

  async search(params: ConsumerSearchParams): Promise<SearchResponse> {
    try {
      const response = await axios.get<SearchResponse>(`${this.baseUrl}/search`, {
        params: {
          ...params,
          maxProfilesToReturn: params.maxProfilesToReturn?.toString(),
          pageNumber: params.pageNumber?.toString(),
          perPage: params.perPage?.toString()
        }
      });
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(`Consumer search failed: ${error.response?.data?.message || error.message}`);
      }
      throw error;
    }
  }
} 