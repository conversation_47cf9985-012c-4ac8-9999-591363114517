import { config } from 'dotenv';

// Load environment variables
config();

export const CONFIG = {
  port: process.env.PORT || 3002,
  nodeEnv: process.env.NODE_ENV || 'development',
  logLevel: process.env.LOG_LEVEL || 'info',
  transactionalServiceUrl: process.env.TRANSACTIONAL_SERVICE_URL || 'http://localhost:3000',
  corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3000',
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    cacheTtl: parseInt(process.env.CACHE_TTL || '300', 10)
  },
  
  // API endpoints
  endpoints: {
    users: '/api/users',
    agents: '/api/agents',
    devices: '/api/devices',
    profiles: '/api/profiles',
    bulk: '/api/bulk-update',
    dashboard: '/api/user-dashboard'
  }
} as const;

export type Config = typeof CONFIG; 