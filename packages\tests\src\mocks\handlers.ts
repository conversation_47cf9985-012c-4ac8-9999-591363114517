import { http, HttpResponse } from 'msw';
import type { User, Agent, Device, Profile } from '@agentworks/utils';

const mockUser: User = {
  id: 'test-user-1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'user',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

const mockAgent: Agent = {
  id: 'test-agent-1',
  userId: 'test-user-1',
  status: 'active',
  capabilities: ['search', 'analyze'],
  lastActive: new Date().toISOString()
};

const mockDevice: Device = {
  id: 'test-device-1',
  userId: 'test-user-1',
  type: 'mobile',
  name: 'Test Phone',
  status: 'online',
  lastSeen: new Date().toISOString()
};

const mockProfile: Profile = {
  id: 'test-profile-1',
  userId: 'test-user-1',
  preferences: {},
  settings: {},
  updatedAt: new Date().toISOString()
};

export const handlers = [
  http.get('/api/users/:userId', () => {
    return HttpResponse.json(mockUser);
  }),

  http.get('/api/agents/:agentId', () => {
    return HttpResponse.json(mockAgent);
  }),

  http.get('/api/devices/:deviceId', () => {
    return HttpResponse.json(mockDevice);
  }),

  http.get('/api/profiles/:profileId', () => {
    return HttpResponse.json(mockProfile);
  })
]; 