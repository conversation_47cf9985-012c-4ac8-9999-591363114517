import React, { useState } from 'react';
import { View, Text, TextInput, StyleSheet, Pressable } from 'react-native';
import Button from '../Button';
import { config } from '../../config';
import { Picker } from '@react-native-picker/picker';

interface Name {
  firstName: string;
  middleName?: string;
  lastName: string;
  secondLastName?: string;
}

interface Address {
  line1: string;
  line2?: string;
  line3?: string;
  city: string;
  countrySubdivision: string;
  countryCode: string;
  postalCode: string;
}

interface MobilePhone {
  number: string;
  countryDialCode: string;
}

export interface Profile {
  name: Name;
  address: Address;
  mobilePhone: MobilePhone;
  rewardsNumber?: string;
  profileId: string;
}

interface PaginationMetadata {
  totalItems: number;
  currentPage: number;
  perPage: number;
  totalPages: number;
}

interface SearchResponse {
  profiles: Profile[];
  paginationMetadata: PaginationMetadata;
}

export interface CustomerSearchProps {
  onCustomerSelect?: (customer: Profile) => void;
}

// Identification type options
const IDENTIFICATION_TYPES = [
  { label: 'Select Type', value: '' },
  { label: 'Driver License', value: 'Drivers License' },
  { label: 'Passport', value: 'Passport' },
  { label: 'Government ID', value: 'Government ID' }
] as const;

type IdentificationType = typeof IDENTIFICATION_TYPES[number]['value'];

export const CustomerSearch: React.FC<CustomerSearchProps> = ({ onCustomerSelect }) => {
  const [rewardsNumber, setRewardsNumber] = useState('');
  const [mobilePhone, setMobilePhone] = useState('');
  const [identificationType, setIdentificationType] = useState<IdentificationType>('');
  const [identificationNumber, setIdentificationNumber] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResponse | null>(null);
  const [selectedProfileId, setSelectedProfileId] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSearch = async () => {
    try {
      setLoading(true);
      setError(null);
      setSelectedProfileId(null);

      const params = new URLSearchParams();
      if (rewardsNumber) params.append('rewardsNumber', rewardsNumber);
      if (mobilePhone) params.append('mobilePhone', mobilePhone);
      if (identificationType) params.append('identificationType', identificationType);
      if (identificationNumber) params.append('identificationNumber', identificationNumber);

      const response = await fetch(`${config.bffWebUrl}/api/consumer/search?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => null);
        throw new Error(errorData?.message || `Search failed with status: ${response.status}`);
      }

      const data = await response.json();
      setSearchResults(data);
    } catch (err) {
      console.error('Search error:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while searching');
      setSearchResults(null);
    } finally {
      setLoading(false);
    }
  };

  const handleCustomerSelect = (customer: Profile) => {
    if (onCustomerSelect) {
      onCustomerSelect(customer);
    }
  };

  return (
    <View style={styles.container}>
      {/* Search Fields */}
      <View style={styles.searchFields}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Rewards Number</Text>
          <TextInput
            style={styles.input}
            value={rewardsNumber}
            onChangeText={setRewardsNumber}
            placeholder="Enter rewards number"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Mobile Phone</Text>
          <TextInput
            style={styles.input}
            value={mobilePhone}
            onChangeText={setMobilePhone}
            placeholder="Enter mobile phone"
            inputMode="tel"
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Identification Type</Text>
          <View style={styles.pickerContainer}>
            <Picker
              style={styles.picker}
              selectedValue={identificationType}
              onValueChange={(value: IdentificationType) => setIdentificationType(value)}
            >
              {IDENTIFICATION_TYPES.map((type) => (
                <Picker.Item key={type.value} label={type.label} value={type.value} />
              ))}
            </Picker>
          </View>
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Identification Number</Text>
          <TextInput
            style={styles.input}
            value={identificationNumber}
            onChangeText={setIdentificationNumber}
            placeholder="Enter identification number"
            editable={!!identificationType}
          />
        </View>

        <View style={styles.buttonContainer}>
          <Button
            label="Search"
            onPress={handleSearch}
            disabled={loading || (!rewardsNumber && !mobilePhone && !identificationNumber)}
          />
        </View>
      </View>

      {/* Error Message */}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {/* Results Grid */}
      {searchResults && searchResults.profiles.length > 0 && (
        <View style={styles.gridContainer}>
          {/* Grid Header */}
          <View style={styles.gridHeader}>
            <View style={[styles.headerCellContainer, styles.flex0_5, styles.radioHeaderCell]}>
              <Text style={styles.headerCellText}>Select</Text>
            </View>
            <View style={[styles.headerCellContainer, styles.flex2]}>
              <Text style={styles.headerCellText}>Name</Text>
            </View>
            <View style={[styles.headerCellContainer, styles.flex2]}>
              <Text style={styles.headerCellText}>Address</Text>
            </View>
            <View style={[styles.headerCellContainer, styles.flex1]}>
              <Text style={styles.headerCellText}>Mobile Phone</Text>
            </View>
            <View style={[styles.headerCellContainer, styles.flex1]}>
              <Text style={styles.headerCellText}>Rewards Number</Text>
            </View>
          </View>

          {/* Grid Rows */}
          {searchResults.profiles.map((profile) => (
            <View
              key={profile.profileId}
              style={styles.gridRow}
            >
              <View style={[styles.cell, styles.flex0_5, styles.radioCell]}>
                <Pressable
                  style={styles.radioButton}
                  onPress={() => {
                    setSelectedProfileId(profile.profileId);
                    handleCustomerSelect(profile);
                  }}
                >
                  <View style={styles.radioOuter}>
                    <View style={[
                      styles.radioInner,
                      selectedProfileId === profile.profileId && styles.radioInnerSelected
                    ]} />
                  </View>
                </Pressable>
              </View>

              <View style={[styles.cell, styles.flex2]}>
                <Text style={styles.primaryText}>
                  {[profile.name.firstName, profile.name.middleName, profile.name.lastName, profile.name.secondLastName]
                    .filter(Boolean)
                    .join(' ')}
                </Text>
              </View>

              <View style={[styles.cell, styles.flex2]}>
                <Text style={styles.primaryText}>
                  {[profile.address.line1, profile.address.line2, profile.address.line3]
                    .filter(Boolean)
                    .join(', ')}
                </Text>
                <Text style={styles.secondaryText}>
                  {`${profile.address.city}, ${profile.address.countrySubdivision} ${profile.address.postalCode}`}
                </Text>
              </View>

              <View style={[styles.cell, styles.flex1]}>
                <Text style={styles.primaryText}>
                  {`${profile.mobilePhone.countryDialCode} ${profile.mobilePhone.number}`}
                </Text>
              </View>

              <View style={[styles.cell, styles.flex1]}>
                <Text style={styles.primaryText}>
                  {profile.rewardsNumber || 'N/A'}
                </Text>
              </View>
            </View>
          ))}

          {/* Pagination Info */}
          <View style={styles.paginationInfo}>
            <Text style={styles.paginationText}>
              Showing {searchResults.paginationMetadata.currentPage * searchResults.paginationMetadata.perPage - searchResults.paginationMetadata.perPage + 1}
              - {Math.min(searchResults.paginationMetadata.currentPage * searchResults.paginationMetadata.perPage, searchResults.paginationMetadata.totalItems)}
              of {searchResults.paginationMetadata.totalItems} results
            </Text>
          </View>
        </View>
      )}

      {/* No Results Message */}
      {searchResults && searchResults.profiles.length === 0 && (
        <Text style={styles.noResultsText}>No customers found</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchFields: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 20,
    alignItems: 'flex-end',
    flexWrap: 'wrap',
  },
  inputGroup: {
    flex: 1,
    minWidth: 200,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
  },
  buttonContainer: {
    marginBottom: 2,
  },
  errorText: {
    color: 'red',
    marginBottom: 16,
  },
  noResultsText: {
    textAlign: 'center',
    marginTop: 20,
    color: '#666',
  },
  gridContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 4,
    overflow: 'hidden',
  },
  gridHeader: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    padding: 12,
  },
  headerCellContainer: {
    padding: 12,
  },
  headerCellText: {
    fontWeight: 'bold',
    fontSize: 14,
    color: '#333',
  },
  radioHeaderCell: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  gridRow: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    padding: 12,
    backgroundColor: '#fff',
  },
  cell: {
    paddingHorizontal: 8,
  },
  flex1: {
    flex: 1,
  },
  flex2: {
    flex: 2,
  },
  primaryText: {
    fontSize: 14,
    color: '#333',
  },
  secondaryText: {
    fontSize: 12,
    color: '#666',
    marginTop: 4,
  },
  selectButton: {
    height: 32,
    paddingHorizontal: 12,
  },
  paginationInfo: {
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  paginationText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'right',
  },
  flex0_5: {
    flex: 0.5,
  },
  radioCell: {
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 4,
  },
  radioButton: {
    padding: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioOuter: {
    height: 20,
    width: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: '#0066cc',
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioInner: {
    height: 10,
    width: 10,
    borderRadius: 5,
    backgroundColor: 'transparent',
  },
  radioInnerSelected: {
    backgroundColor: '#0066cc',
  },
  pickerContainer: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 4,
    backgroundColor: '#fff',
  },
  picker: {
    height: 40,
    width: '100%',
  },
});