import { Router, Request, Response } from 'express';
import { FeeQuoteClient } from '../clients/fee-quote.client';
import type { FeeQuoteRequest } from '@agentworks/transactional-service/src/models/fee-quote.request';
import logger from '../config/logger';
import { v4 as uuidv4 } from 'uuid';

const router: Router = Router();
const feeQuoteClient = new FeeQuoteClient();

// Get fee quote
router.post('/quote/sendamount', async (req: Request<any, any, FeeQuoteRequest>, res: Response) => {
  const requestId = uuidv4();
  
  logger.info('=== Fee Quote Request Started ===', {
    requestId,
    timestamp: new Date().toISOString(),
    method: 'POST',
    path: '/api/fee-quote/quote/sendamount',
    clientIp: req.ip,
    userAgent: req.get('user-agent'),
    requestBody: req.body
  });

  try {
    const quoteRequest: FeeQuoteRequest = req.body;
    const startTime = Date.now();
    const quote = await feeQuoteClient.getQuote(quoteRequest);
    const duration = Date.now() - startTime;
    
    logger.info('=== Fee Quote Response Success ===', {
      requestId,
      timestamp: new Date().toISOString(),
      duration: `${duration}ms`,
      status: 200,
      responseBody: {
        transactionCount: quote.transactions?.length || 0
      }
    });

    res.json(quote);
  } catch (error: any) {
    logger.error('=== Fee Quote Error ===', {
      requestId,
      timestamp: new Date().toISOString(),
      status: error.status || 500,
      errorDetails: {
        message: error.message,
        code: error.code,
        response: error.response?.data,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      }
    });

    res.status(error.status || 500).json(error);
  } finally {
    logger.info('=== Fee Quote Request Completed ===', {
      requestId,
      timestamp: new Date().toISOString()
    });
  }
});

export default router; 