import { config } from 'dotenv';

// Load environment variables
config();

export const CONFIG = {
  port: process.env.PORT || 3001,
  nodeEnv: process.env.NODE_ENV || 'development',
  logLevel: process.env.LOG_LEVEL || 'info',
  transactionalServiceUrl: process.env.TRANSACTIONAL_SERVICE_URL || 'http://localhost:3000',
  corsOrigin: (process.env.CORS_ORIGIN || 'http://localhost:3000,http://localhost:3002').split(','),
  
  // API endpoints
  endpoints: {
    users: '/api/users',
    agents: '/api/agents',
    devices: '/api/devices',
    profiles: '/api/profiles',
    batch: '/api/batch-update',
    sync: '/api/sync'
  }
} as const;

export type Config = typeof CONFIG; 