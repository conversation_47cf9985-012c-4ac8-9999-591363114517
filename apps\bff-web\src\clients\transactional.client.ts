import axios, { AxiosInstance } from 'axios';
import { config } from '../config';

// Import the types from transactional service
import type { QuoteRequest } from '@agentworks/transactional-service/src/models/quote.request';
import type { QuoteResponse, QuoteErrorResponse } from '@agentworks/transactional-service/src/models/quote.response';

export class TransactionalClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: config.services.transactional.url,
      timeout: 5000,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }

  async getQuote(quoteRequest: QuoteRequest): Promise<QuoteResponse> {
    try {
      const response = await this.client.post<QuoteResponse>(
        '/api/transactional/quote',
        quoteRequest
      );
      return response.data;
    } catch (error: any) {
      if (error.response?.data) {
        throw error.response.data as QuoteErrorResponse;
      }
      throw {
        status: 500,
        message: 'Failed to get quote',
        errors: [{
          code: '500',
          message: error.message || 'Unknown error'
        }]
      } as QuoteErrorResponse;
    }
  }

  async updateTransaction(transactionId: string, updateData: any) {
    const response = await this.client.put(
      `/api/transactions/${transactionId}`,
      updateData
    );
    return response.data;
  }

  async commitTransaction(transactionId: string) {
    const response = await this.client.put(
      `/api/transactions/${transactionId}/commit`
    );
    return response.data;
  }
} 